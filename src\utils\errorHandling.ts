/**
 * Enhanced Error Handling Utilities
 * Provides consistent error handling across the application
 */

export interface DatabaseError {
  code?: string;
  message: string;
  details?: string;
  hint?: string;
}

export interface ErrorHandlingOptions {
  silent?: boolean;
  fallbackValue?: any;
  logLevel?: 'error' | 'warn' | 'info';
  context?: string;
}

/**
 * Handle Supabase database errors with consistent logging and fallbacks
 */
export function handleDatabaseError(
  error: DatabaseError | any,
  options: ErrorHandlingOptions = {}
): any {
  const {
    silent = false,
    fallbackValue = null,
    logLevel = 'error',
    context = 'Database operation'
  } = options;

  // Handle specific Supabase error codes
  if (error?.code) {
    switch (error.code) {
      case 'PGRST116':
        // No rows found - this is often expected
        if (!silent && logLevel === 'error') {
          console.info(`${context}: No rows found`);
        }
        return fallbackValue;

      case 'PGRST301':
        // JWT expired
        if (!silent) {
          console.warn(`${context}: Authentication expired - ${error.message}`);
        }
        return fallbackValue;

      case 'PGRST202':
        // Function or table not found
        if (!silent) {
          console.warn(`${context}: Database schema issue - ${error.message}`);
        }
        return fallbackValue;

      case 'PGRST204':
        // Not acceptable (406)
        if (!silent) {
          console.warn(`${context}: Request not acceptable - ${error.message}`);
        }
        return fallbackValue;

      case '23505':
        // Unique constraint violation
        if (!silent) {
          console.warn(`${context}: Duplicate entry - ${error.message}`);
        }
        return fallbackValue;

      case '23503':
        // Foreign key constraint violation
        if (!silent) {
          console.warn(`${context}: Reference constraint violation - ${error.message}`);
        }
        return fallbackValue;

      default:
        // Unknown database error
        if (!silent) {
          console[logLevel](`${context}: Database error [${error.code}] - ${error.message}`);
        }
        return fallbackValue;
    }
  }

  // Handle generic errors
  if (error?.message) {
    if (error.message.includes('JWT expired') || error.message.includes('invalid JWT')) {
      if (!silent) {
        console.warn(`${context}: Authentication expired`);
      }
      return fallbackValue;
    }

    if (error.message.includes('timeout') || error.message.includes('TIMEOUT')) {
      if (!silent) {
        console.warn(`${context}: Operation timed out`);
      }
      return fallbackValue;
    }

    if (error.message.includes('Network Error') || error.message.includes('fetch failed')) {
      if (!silent) {
        console.warn(`${context}: Network connectivity issue`);
      }
      return fallbackValue;
    }
  }

  // Log unknown errors
  if (!silent) {
    console[logLevel](`${context}: Unknown error`, error);
  }

  return fallbackValue;
}

/**
 * Wrapper for database operations with automatic error handling
 */
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  options: ErrorHandlingOptions = {}
): Promise<T | any> {
  try {
    return await operation();
  } catch (error) {
    return handleDatabaseError(error, options);
  }
}

/**
 * Retry mechanism for failed operations
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000,
  options: ErrorHandlingOptions = {}
): Promise<T | any> {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;

      // Don't retry for certain error types
      if (error?.code === 'PGRST301' || error?.message?.includes('JWT expired')) {
        return handleDatabaseError(error, options);
      }

      if (error?.code === 'PGRST116') {
        // No rows found - don't retry
        return handleDatabaseError(error, options);
      }

      if (attempt === maxRetries) {
        break;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  return handleDatabaseError(lastError, options);
}

/**
 * Safe async operation wrapper that never throws
 */
export async function safeAsync<T>(
  operation: () => Promise<T>,
  fallbackValue: T | null = null
): Promise<T | null> {
  try {
    return await operation();
  } catch (error) {
    console.warn('Safe async operation failed:', error);
    return fallbackValue;
  }
}

/**
 * Debounced error logger to prevent spam
 */
class DebouncedLogger {
  private errorCounts = new Map<string, number>();
  private lastLogTime = new Map<string, number>();
  private readonly debounceMs = 5000; // 5 seconds

  log(key: string, message: string, level: 'error' | 'warn' | 'info' = 'error') {
    const now = Date.now();
    const lastLog = this.lastLogTime.get(key) || 0;
    const count = this.errorCounts.get(key) || 0;

    if (now - lastLog > this.debounceMs) {
      if (count > 1) {
        console[level](`${message} (occurred ${count} times)`);
      } else {
        console[level](message);
      }
      this.errorCounts.set(key, 1);
      this.lastLogTime.set(key, now);
    } else {
      this.errorCounts.set(key, count + 1);
    }
  }
}

export const debouncedLogger = new DebouncedLogger();

/**
 * Enhanced console error that prevents spam
 */
export function logError(context: string, error: any, details?: any) {
  const key = `${context}-${error?.code || error?.message || 'unknown'}`;
  const message = `[${context}] ${error?.message || error}`;
  
  debouncedLogger.log(key, message, 'error');
  
  if (details && process.env.NODE_ENV === 'development') {
    console.debug(`[${context}] Details:`, details);
  }
}

/**
 * Enhanced console warning that prevents spam
 */
export function logWarning(context: string, message: string, details?: any) {
  const key = `${context}-${message}`;
  
  debouncedLogger.log(key, `[${context}] ${message}`, 'warn');
  
  if (details && process.env.NODE_ENV === 'development') {
    console.debug(`[${context}] Details:`, details);
  }
}
