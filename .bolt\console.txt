client:802 WebSocket connection to 'ws://localhost:5173/?token=L1gNoi5rgDyA' failed: 
createConnection @ client:802
connect @ client:437
connect @ client:811
connect @ client:290
connect @ client:383
(anonymous) @ client:908
client:841 [vite] failed to connect to websocket (Error: WebSocket closed without opened.). 
connect @ client:841
await in connect
connect @ client:290
connect @ client:383
(anonymous) @ client:908
client:454 Uncaught (in promise) Error: WebSocket closed without opened.
    at WebSocket.<anonymous> (client:454:22)
(anonymous) @ client:454
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/feature_flags?select=*&order=category.asc%2Cfeature_name.asc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
AuthContext.tsx:148 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/users?select=id&id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
setTimeout
(anonymous) @ AuthContext.tsx:148
(anonymous) @ GoTrueClient.ts:2041
_notifyAllSubscribers @ GoTrueClient.ts:2039
_recoverAndRefresh @ GoTrueClient.ts:1961
await in _recoverAndRefresh
_initialize @ GoTrueClient.ts:380
await in _initialize
(anonymous) @ GoTrueClient.ts:305
(anonymous) @ GoTrueClient.ts:995
(anonymous) @ locks.ts:107
ensureUserRow.ts:16 Error checking user row: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
ensureUserRowExists @ ensureUserRow.ts:16
await in ensureUserRowExists
checkUserRow @ AuthContext.tsx:77
(anonymous) @ AuthContext.tsx:163
setTimeout
(anonymous) @ AuthContext.tsx:148
(anonymous) @ GoTrueClient.ts:2041
_notifyAllSubscribers @ GoTrueClient.ts:2039
_recoverAndRefresh @ GoTrueClient.ts:1961
await in _recoverAndRefresh
_initialize @ GoTrueClient.ts:380
await in _initialize
(anonymous) @ GoTrueClient.ts:305
(anonymous) @ GoTrueClient.ts:995
(anonymous) @ locks.ts:107
AuthContext.tsx:80 Error in checkUserRow: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
checkUserRow @ AuthContext.tsx:80
await in checkUserRow
(anonymous) @ AuthContext.tsx:163
setTimeout
(anonymous) @ AuthContext.tsx:148
(anonymous) @ GoTrueClient.ts:2041
_notifyAllSubscribers @ GoTrueClient.ts:2039
_recoverAndRefresh @ GoTrueClient.ts:1961
await in _recoverAndRefresh
_initialize @ GoTrueClient.ts:380
await in _initialize
(anonymous) @ GoTrueClient.ts:305
(anonymous) @ GoTrueClient.ts:995
(anonymous) @ locks.ts:107
react-dom.development.js:86 Warning: Received `true` for a non-boolean attribute `jsx`.

If you want to write it to the DOM, pass a string instead: jsx="true" or jsx={value.toString()}.
    at style
    at AnnouncementToastManager (http://localhost:5174/src/components/user/AnnouncementToast.tsx:181:3)
    at div
    at DashboardLayout (http://localhost:5174/src/layouts/DashboardLayout.tsx:37:21)
    at RenderedRoute (http://localhost:5174/.vite_cache/deps/react-router-dom.js?v=3be051aa:4108:5)
    at Routes (http://localhost:5174/.vite_cache/deps/react-router-dom.js?v=3be051aa:4578:5)
    at AppRoutes (http://localhost:5174/src/routes.tsx:105:29)
    at div
    at NotificationProvider (http://localhost:5174/src/contexts/NotificationContext.tsx:30:40)
    at UserGuidanceProvider (http://localhost:5174/src/contexts/UserGuidanceContext.tsx:1162:40)
    at ShortcutProvider (http://localhost:5174/src/contexts/ShortcutContext.tsx:44:36)
    at SearchProvider (http://localhost:5174/src/contexts/SearchContext.tsx:29:34)
    at OfflineProvider (http://localhost:5174/src/contexts/OfflineContext.tsx:28:35)
    at UIProviders (http://localhost:5174/src/providers/UIProviders.tsx:29:31)
    at PaymentScheduleProvider (http://localhost:5174/src/contexts/PaymentScheduleContext.tsx:31:43)
    at CreditCardRewardsProvider (http://localhost:5174/src/contexts/CreditCardRewardsContext.tsx:35:45)
    at CreditScoreProvider (http://localhost:5174/src/contexts/CreditScoreContext.tsx:34:39)
    at CreditCardProvider (http://localhost:5174/src/contexts/CreditCardContext.tsx:39:38)
    at BillProvider (http://localhost:5174/src/contexts/BillContext.tsx:29:32)
    at LoanProvider (http://localhost:5174/src/contexts/LoanContext.tsx:50:32)
    at InvestmentProvider (http://localhost:5174/src/contexts/InvestmentContext.tsx:51:38)
    at BudgetProvider (http://localhost:5174/src/contexts/BudgetContext.tsx:35:34)
    at TransactionHistoryProvider (http://localhost:5174/src/contexts/TransactionHistoryContext.tsx:56:46)
    at PaymentMethodProvider (http://localhost:5174/src/contexts/PaymentMethodContext.tsx:41:41)
    at CategoryProvider (http://localhost:5174/src/contexts/CategoryContext.tsx:67:36)
    at FinancialProviders (http://localhost:5174/src/providers/FinancialProviders.tsx:35:38)
    at AccessibilityProvider (http://localhost:5174/src/components/AccessibilityProvider.tsx:42:41)
    at ErrorBoundary (http://localhost:5174/.vite_cache/deps/react-error-boundary.js?v=914cb7a3:31:5)
    at AppErrorBoundary (http://localhost:5174/src/components/ErrorBoundary.tsx:187:36)
    at Router (http://localhost:5174/.vite_cache/deps/react-router-dom.js?v=3be051aa:4521:15)
    at BrowserRouter (http://localhost:5174/.vite_cache/deps/react-router-dom.js?v=3be051aa:5267:5)
    at FeatureControlProvider (http://localhost:5174/src/contexts/FeatureControlContext.tsx:29:42)
    at SmartCategoriesProvider (http://localhost:5174/src/contexts/SmartCategoriesContext.tsx:39:43)
    at RealTimeProvider (http://localhost:5174/src/contexts/RealTimeContext.tsx:31:36)
    at UserPreferencesProvider (http://localhost:5174/src/contexts/UserPreferencesContext.tsx:32:43)
    at CurrencyProvider (http://localhost:5174/src/contexts/CurrencyContext.tsx:46:36)
    at AIServiceProvider (http://localhost:5174/src/contexts/AIServiceContext.tsx:32:37)
    at SettingsProvider (http://localhost:5174/src/contexts/SettingsContext.tsx:107:36)
    at TwoFactorProvider (http://localhost:5174/src/contexts/TwoFactorContext.tsx:31:37)
    at AuthProvider (http://localhost:5174/src/contexts/AuthContext.tsx:48:32)
    at ThemeProvider (http://localhost:5174/src/contexts/ThemeContext.tsx:27:33)
    at CoreProviders (http://localhost:5174/src/providers/CoreProviders.tsx:34:33)
    at QueryClientProvider (http://localhost:5174/.vite_cache/deps/chunk-QQM42VI5.js?v=435ffbc3:2932:3)
    at App (http://localhost:5174/src/App.tsx:54:3)
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateProperty$1 @ react-dom.development.js:3765
warnUnknownProperties @ react-dom.development.js:3803
validateProperties$2 @ react-dom.development.js:3827
validatePropertiesInDevelopment @ react-dom.development.js:9541
setInitialProperties @ react-dom.development.js:9830
finalizeInitialChildren @ react-dom.development.js:10950
completeWork @ react-dom.development.js:22232
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
FeatureControlService.ts:75 Failed to fetch feature flags: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
getAllFeatures @ FeatureControlService.ts:75
await in getAllFeatures
loadFeatures @ FeatureControlContext.tsx:31
(anonymous) @ FeatureControlContext.tsx:23
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
performSyncWorkOnRoot @ react-dom.development.js:26115
flushSyncCallbacks @ react-dom.development.js:12042
commitRootImpl @ react-dom.development.js:26998
commitRoot @ react-dom.development.js:26721
finishConcurrentRender @ react-dom.development.js:26020
performConcurrentWorkOnRoot @ react-dom.development.js:25848
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
helpers.ts:105 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/auth/v1/user 403 (Forbidden)
(anonymous) @ helpers.ts:105
_handleRequest2 @ fetch.ts:184
_request @ fetch.ts:157
(anonymous) @ GoTrueClient.ts:1213
_useSession @ GoTrueClient.ts:1065
await in _useSession
_getUser @ GoTrueClient.ts:1202
(anonymous) @ GoTrueClient.ts:1186
(anonymous) @ GoTrueClient.ts:995
(anonymous) @ locks.ts:107
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/feature_flags?select=*&order=category.asc%2Cfeature_name.asc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
FeatureControlService.ts:75 Failed to fetch feature flags: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
getAllFeatures @ FeatureControlService.ts:75
await in getAllFeatures
loadFeatures @ FeatureControlContext.tsx:31
(anonymous) @ FeatureControlContext.tsx:23
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
performSyncWorkOnRoot @ react-dom.development.js:26115
flushSyncCallbacks @ react-dom.development.js:12042
commitRootImpl @ react-dom.development.js:26998
commitRoot @ react-dom.development.js:26721
finishConcurrentRender @ react-dom.development.js:26020
performConcurrentWorkOnRoot @ react-dom.development.js:25848
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
createUserPreferences.ts:21 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/auth/v1/user 403 (Forbidden)
(anonymous) @ helpers.ts:105
_handleRequest2 @ fetch.ts:184
_request @ fetch.ts:157
(anonymous) @ GoTrueClient.ts:1213
_useSession @ GoTrueClient.ts:1065
await in _useSession
_getUser @ GoTrueClient.ts:1202
(anonymous) @ GoTrueClient.ts:1186
(anonymous) @ GoTrueClient.ts:973
await in (anonymous)
_acquireLock @ GoTrueClient.ts:974
getUser @ GoTrueClient.ts:1185
await in getUser
createUserPreferences @ createUserPreferences.ts:21
initUserPreferences @ UserPreferencesContext.tsx:49
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:12.512Z] [CreateUserPreferences] Failed to get current user {error: AuthApiError: invalid JWT: unable to parse or verify signature, token has invalid claims: token is …}
log @ logger.ts:119
error @ logger.ts:155
createUserPreferences @ createUserPreferences.ts:24
await in createUserPreferences
initUserPreferences @ UserPreferencesContext.tsx:49
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:12.523Z] [CreateUserPreferences] Failed to get current user {error: AuthApiError: invalid JWT: unable to parse or verify signature, token has invalid claims: token is …}
log @ logger.ts:119
error @ logger.ts:155
createUserPreferences @ createUserPreferences.ts:24
await in createUserPreferences
initUserPreferences @ UserPreferencesContext.tsx:49
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
AuthContext.tsx:148 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/users?select=id&id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
setTimeout
(anonymous) @ AuthContext.tsx:148
(anonymous) @ GoTrueClient.ts:1673
_useSession @ GoTrueClient.ts:1065
await in _useSession
_emitInitialSession @ GoTrueClient.ts:1665
(anonymous) @ GoTrueClient.ts:1657
(anonymous) @ GoTrueClient.ts:995
(anonymous) @ locks.ts:107
ensureUserRow.ts:16 Error checking user row: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
ensureUserRowExists @ ensureUserRow.ts:16
await in ensureUserRowExists
checkUserRow @ AuthContext.tsx:77
(anonymous) @ AuthContext.tsx:163
setTimeout
(anonymous) @ AuthContext.tsx:148
(anonymous) @ GoTrueClient.ts:1673
_useSession @ GoTrueClient.ts:1065
await in _useSession
_emitInitialSession @ GoTrueClient.ts:1665
(anonymous) @ GoTrueClient.ts:1657
(anonymous) @ GoTrueClient.ts:995
(anonymous) @ locks.ts:107
AuthContext.tsx:80 Error in checkUserRow: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
checkUserRow @ AuthContext.tsx:80
await in checkUserRow
(anonymous) @ AuthContext.tsx:163
setTimeout
(anonymous) @ AuthContext.tsx:148
(anonymous) @ GoTrueClient.ts:1673
_useSession @ GoTrueClient.ts:1065
await in _useSession
_emitInitialSession @ GoTrueClient.ts:1665
(anonymous) @ GoTrueClient.ts:1657
(anonymous) @ GoTrueClient.ts:995
(anonymous) @ locks.ts:107
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/credit_card_rewards?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=date.desc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
creditCards.ts:869 Error getting credit card rewards: Error: Failed to get credit card rewards: JWT expired
    at getCreditCardRewards (creditCards.ts:864:13)
    at async Promise.all (index 0)
    at async refreshRewards (CreditCardRewardsContext.tsx:63:40)
getCreditCardRewards @ creditCards.ts:869
await in getCreditCardRewards
refreshRewards @ CreditCardRewardsContext.tsx:64
(anonymous) @ CreditCardRewardsContext.tsx:80
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           POST https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/rpc/get_unread_notification_count 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/credit_cards?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&status=eq.active 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
NotificationContext.tsx:59 Error fetching notifications: Error: Failed to get unread count: JWT expired
    at NotificationService.getUnreadCount (NotificationService.ts:108:13)
    at async Promise.all (index 1)
    at async NotificationContext.tsx:49:52
(anonymous) @ NotificationContext.tsx:59
await in (anonymous)
(anonymous) @ NotificationContext.tsx:185
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/transactions?select=count&limit=1 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/user_preferences?select=seen_tutorials&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/feature_flags?select=*&order=category.asc%2Cfeature_name.asc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/users?select=settings&id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/credit_cards?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=card_name.asc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
DataSourceChecker.tsx:81 Database connection failed: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
checkDataSources @ DataSourceChecker.tsx:81
await in checkDataSources
(anonymous) @ DataSourceChecker.tsx:118
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
UserGuidanceContext.tsx:389 Error loading seen tutorials: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
loadSeenTutorials @ UserGuidanceContext.tsx:389
await in loadSeenTutorials
(anonymous) @ UserGuidanceContext.tsx:393
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
FeatureControlService.ts:75 Failed to fetch feature flags: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
getAllFeatures @ FeatureControlService.ts:75
await in getAllFeatures
loadFeatures @ FeatureControlContext.tsx:31
(anonymous) @ FeatureControlContext.tsx:23
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/users?select=id%2Cemail%2Cname%2Cprofile_picture_url%2Cphone_number%2Caddress%2Cdate_of_birth%2Coccupation%2Cpreferred_currency%2Ctax_id%2Ctwo_factor_enabled%2Ccreated_at%2Cupdated_at&id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
profile.ts:83 Error fetching user profile: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
getUserProfile @ profile.ts:83
await in getUserProfile
(anonymous) @ useUserProfile.ts:26
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/notifications?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&or=%28expires_at.is.null%2Cexpires_at.gt.now%28%29%29&order=created_at.desc&limit=50 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/loans?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=start_date.desc&offset=0&limit=20 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
loans.ts:49 Error fetching loans: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
(anonymous) @ loans.ts:49
await in (anonymous)
retry @ loans.ts:11
getLoans @ loans.ts:45
fetchLoans @ LoanContext.tsx:135
(anonymous) @ LoanContext.tsx:274
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/investments?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=purchase_date.desc&offset=0&limit=20 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
investments.ts:47 Error fetching investments: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
(anonymous) @ investments.ts:47
await in (anonymous)
retry @ investments.ts:9
getInvestments @ investments.ts:43
fetchInvestments @ InvestmentContext.tsx:165
(anonymous) @ InvestmentContext.tsx:187
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/transactions?select=id%2Camount%2Ctype%2Ccategory%2Csource%2Cdate%2Cdescription%2Cmetadata%2Cattachments%2Ccreated_at%2Cupdated_at&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=date.desc&offset=0&limit=20 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/categories?select=id&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
logger.ts:119 [2025-07-02T15:03:13.849Z] [TransactionService] Error fetching transactions {error: {…}}
log @ logger.ts:119
error @ logger.ts:155
(anonymous) @ TransactionService.ts:516
await in (anonymous)
retry @ retry.ts:124
getTransactions @ TransactionService.ts:490
fetchTransactions @ TransactionHistoryContext.tsx:184
(anonymous) @ TransactionHistoryContext.tsx:238
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:13.849Z] [TransactionService] Failed to fetch transactions {error: {…}}
log @ logger.ts:119
error @ logger.ts:155
(anonymous) @ TransactionService.ts:544
await in (anonymous)
retry @ retry.ts:124
getTransactions @ TransactionService.ts:490
fetchTransactions @ TransactionHistoryContext.tsx:184
(anonymous) @ TransactionHistoryContext.tsx:238
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:13.849Z] [ErrorHandling] API error occurred {error: {…}}
log @ logger.ts:119
error @ logger.ts:155
handleApiError @ errorHandling.ts:119
(anonymous) @ TransactionService.ts:545
await in (anonymous)
retry @ retry.ts:124
getTransactions @ TransactionService.ts:490
fetchTransactions @ TransactionHistoryContext.tsx:184
(anonymous) @ TransactionHistoryContext.tsx:238
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/smart_categories_preferences?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
CategoryContext.tsx:142 Error initializing default categories: JWT expired
initializeDefaultCategories @ CategoryContext.tsx:142
await in initializeDefaultCategories
(anonymous) @ CategoryContext.tsx:309
(anonymous) @ CategoryContext.tsx:312
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
SmartCategoriesContext.tsx:74 Error loading smart categories preferences: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
loadPreferences @ SmartCategoriesContext.tsx:74
await in loadPreferences
(anonymous) @ SmartCategoriesContext.tsx:53
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/budgets?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=category.asc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/credit_card_rewards?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=date.desc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/credit_cards?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&status=eq.active 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/users?select=id&id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/user_preferences?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
ensureUserRow.ts:16 Error checking user row: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
ensureUserRowExists @ ensureUserRow.ts:16
await in ensureUserRowExists
checkUserRow @ AuthContext.tsx:77
initializeAuth @ AuthContext.tsx:123
await in initializeAuth
(anonymous) @ AuthContext.tsx:134
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
performSyncWorkOnRoot @ react-dom.development.js:26115
flushSyncCallbacks @ react-dom.development.js:12042
commitRootImpl @ react-dom.development.js:26998
commitRoot @ react-dom.development.js:26721
finishConcurrentRender @ react-dom.development.js:26020
performConcurrentWorkOnRoot @ react-dom.development.js:25848
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
AuthContext.tsx:80 Error in checkUserRow: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
checkUserRow @ AuthContext.tsx:80
await in checkUserRow
initializeAuth @ AuthContext.tsx:123
await in initializeAuth
(anonymous) @ AuthContext.tsx:134
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
performSyncWorkOnRoot @ react-dom.development.js:26115
flushSyncCallbacks @ react-dom.development.js:12042
commitRootImpl @ react-dom.development.js:26998
commitRoot @ react-dom.development.js:26721
finishConcurrentRender @ react-dom.development.js:26020
performConcurrentWorkOnRoot @ react-dom.development.js:25848
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/notification_preferences?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
logger.ts:119 [2025-07-02T15:03:14.025Z] [UserPreferences] Error fetching user preferences {error: {…}}
log @ logger.ts:119
error @ logger.ts:155
(anonymous) @ userPreferences.ts:52
await in (anonymous)
retry @ retry.ts:124
getUserPreferences @ userPreferences.ts:38
(anonymous) @ useUserPreferences.ts:41
initUserPreferences @ UserPreferencesContext.tsx:51
await in initUserPreferences
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
helpers.ts:105 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/auth/v1/user 403 (Forbidden)
(anonymous) @ helpers.ts:105
_handleRequest2 @ fetch.ts:184
_request @ fetch.ts:157
(anonymous) @ GoTrueClient.ts:1213
_useSession @ GoTrueClient.ts:1065
await in _useSession
_getUser @ GoTrueClient.ts:1202
(anonymous) @ GoTrueClient.ts:1186
(anonymous) @ GoTrueClient.ts:995
(anonymous) @ locks.ts:107
NotificationContext.tsx:76 Error fetching notification preferences: Error: Failed to get notification preferences: JWT expired
    at NotificationService.getPreferences (NotificationService.ts:186:13)
    at async NotificationContext.tsx:73:31
(anonymous) @ NotificationContext.tsx:76
await in (anonymous)
(anonymous) @ NotificationContext.tsx:186
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
BudgetContext.tsx:170 Error fetching budgets: Error: Failed to fetch budgets: JWT expired
    at getBudgets (budgets.ts:11:20)
    at async fetchBudgets (BudgetContext.tsx:166:20)
fetchBudgets @ BudgetContext.tsx:170
await in fetchBudgets
(anonymous) @ BudgetContext.tsx:178
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/user_preferences?select=seen_tutorials&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/feature_flags?select=*&order=category.asc%2Cfeature_name.asc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
UserGuidanceContext.tsx:389 Error loading seen tutorials: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
loadSeenTutorials @ UserGuidanceContext.tsx:389
await in loadSeenTutorials
(anonymous) @ UserGuidanceContext.tsx:393
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/users?select=settings&id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
FeatureControlService.ts:75 Failed to fetch feature flags: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
getAllFeatures @ FeatureControlService.ts:75
await in getAllFeatures
loadFeatures @ FeatureControlContext.tsx:31
(anonymous) @ FeatureControlContext.tsx:23
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/credit_cards?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=card_name.asc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/users?select=id%2Cemail%2Cname%2Cprofile_picture_url%2Cphone_number%2Caddress%2Cdate_of_birth%2Coccupation%2Cpreferred_currency%2Ctax_id%2Ctwo_factor_enabled%2Ccreated_at%2Cupdated_at&id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/user_2fa_setup?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&is_verified=eq.true 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/credit_card_rewards?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=date.desc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/credit_scores?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=date.asc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/user_preferences?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/smart_categories_preferences?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/transactions?select=count&limit=1 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
SettingsContext.tsx:237 Error fetching settings: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
loadSettings @ SettingsContext.tsx:237
await in loadSettings
(anonymous) @ SettingsContext.tsx:275
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
creditCards.ts:869 Error getting credit card rewards: Error: Failed to get credit card rewards: JWT expired
    at getCreditCardRewards (creditCards.ts:864:13)
    at async getCreditCardRewardStats (creditCards.ts:950:21)
    at async Promise.all (index 1)
getCreditCardRewards @ creditCards.ts:869
await in getCreditCardRewards
getCreditCardRewardStats @ creditCards.ts:950
refreshRewards @ CreditCardRewardsContext.tsx:65
(anonymous) @ CreditCardRewardsContext.tsx:80
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
creditCards.ts:999 Error getting reward statistics: Error: Failed to get credit card rewards: JWT expired
    at getCreditCardRewards (creditCards.ts:864:13)
    at async getCreditCardRewardStats (creditCards.ts:950:21)
    at async Promise.all (index 1)
getCreditCardRewardStats @ creditCards.ts:999
await in getCreditCardRewardStats
refreshRewards @ CreditCardRewardsContext.tsx:65
(anonymous) @ CreditCardRewardsContext.tsx:80
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
profile.ts:83 Error fetching user profile: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
getUserProfile @ profile.ts:83
await in getUserProfile
(anonymous) @ useUserProfile.ts:26
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:14.535Z] [TwoFactorService] Failed to get user 2FA methods {error: {…}, userId: '468c9def-c050-49ae-b823-1dbe7bd47d40'}
log @ logger.ts:119
error @ logger.ts:155
getUserMethods @ TwoFactorService.ts:165
await in getUserMethods
refreshMethods @ TwoFactorContext.tsx:66
(anonymous) @ TwoFactorContext.tsx:53
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
creditCards.ts:869 Error getting credit card rewards: Error: Failed to get credit card rewards: JWT expired
    at getCreditCardRewards (creditCards.ts:864:13)
    at async Promise.all (index 0)
    at async refreshRewards (CreditCardRewardsContext.tsx:63:40)
getCreditCardRewards @ creditCards.ts:869
await in getCreditCardRewards
refreshRewards @ CreditCardRewardsContext.tsx:64
(anonymous) @ CreditCardRewardsContext.tsx:80
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
creditCards.ts:770 Error getting credit scores: Error: Failed to get credit scores: JWT expired
    at getCreditScores (creditCards.ts:765:13)
    at async refreshCreditScores (CreditScoreContext.tsx:45:20)
getCreditScores @ creditCards.ts:770
await in getCreditScores
refreshCreditScores @ CreditScoreContext.tsx:45
(anonymous) @ CreditScoreContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:14.541Z] [UserPreferences] Error fetching user preferences {error: {…}}
log @ logger.ts:119
error @ logger.ts:155
(anonymous) @ userPreferences.ts:52
await in (anonymous)
retry @ retry.ts:124
getUserPreferences @ userPreferences.ts:38
(anonymous) @ useUserPreferences.ts:41
(anonymous) @ useUserPreferences.ts:115
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
SmartCategoriesContext.tsx:74 Error loading smart categories preferences: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
loadPreferences @ SmartCategoriesContext.tsx:74
await in loadPreferences
(anonymous) @ SmartCategoriesContext.tsx:53
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
DataSourceChecker.tsx:81 Database connection failed: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
checkDataSources @ DataSourceChecker.tsx:81
await in checkDataSources
(anonymous) @ DataSourceChecker.tsx:118
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:14.595Z] [CreateUserPreferences] Failed to get current user {error: AuthApiError: invalid JWT: unable to parse or verify signature, token has invalid claims: token is …}
log @ logger.ts:119
error @ logger.ts:155
createUserPreferences @ createUserPreferences.ts:24
await in createUserPreferences
initUserPreferences @ UserPreferencesContext.tsx:49
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/users?select=settings&id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/credit_cards?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=card_name.asc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/credit_card_rewards?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=date.desc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
BillContext.tsx:70 Error fetching bills: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
fetchBills @ BillContext.tsx:70
await in fetchBills
(anonymous) @ BillContext.tsx:79
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
loans.ts:14 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/loans?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=start_date.desc&offset=0&limit=20 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
setTimeout
(anonymous) @ loans.ts:14
retry @ loans.ts:14
await in retry
getLoans @ loans.ts:45
fetchLoans @ LoanContext.tsx:135
(anonymous) @ LoanContext.tsx:274
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
creditCards.ts:869 Error getting credit card rewards: Error: Failed to get credit card rewards: JWT expired
    at getCreditCardRewards (creditCards.ts:864:13)
    at async getCreditCardRewardStats (creditCards.ts:950:21)
    at async Promise.all (index 1)
getCreditCardRewards @ creditCards.ts:869
await in getCreditCardRewards
getCreditCardRewardStats @ creditCards.ts:950
refreshRewards @ CreditCardRewardsContext.tsx:65
(anonymous) @ CreditCardRewardsContext.tsx:80
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
creditCards.ts:999 Error getting reward statistics: Error: Failed to get credit card rewards: JWT expired
    at getCreditCardRewards (creditCards.ts:864:13)
    at async getCreditCardRewardStats (creditCards.ts:950:21)
    at async Promise.all (index 1)
getCreditCardRewardStats @ creditCards.ts:999
await in getCreditCardRewardStats
refreshRewards @ CreditCardRewardsContext.tsx:65
(anonymous) @ CreditCardRewardsContext.tsx:80
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
loans.ts:49 Error fetching loans: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
(anonymous) @ loans.ts:49
await in (anonymous)
retry @ loans.ts:11
await in retry
getLoans @ loans.ts:45
fetchLoans @ LoanContext.tsx:135
(anonymous) @ LoanContext.tsx:274
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
retry.ts:148 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/transactions?select=id%2Camount%2Ctype%2Ccategory%2Csource%2Cdate%2Cdescription%2Cmetadata%2Cattachments%2Ccreated_at%2Cupdated_at&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=date.desc&offset=0&limit=20 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
setTimeout
logger.error.lastError.lastError @ retry.ts:148
retry @ retry.ts:148
await in retry
getTransactions @ TransactionService.ts:490
fetchTransactions @ TransactionHistoryContext.tsx:184
(anonymous) @ TransactionHistoryContext.tsx:238
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/transactions?select=count&limit=1 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
logger.ts:119 [2025-07-02T15:03:15.017Z] [TransactionService] Error fetching transactions {error: {…}}
log @ logger.ts:119
error @ logger.ts:155
(anonymous) @ TransactionService.ts:516
await in (anonymous)
retry @ retry.ts:124
await in retry
getTransactions @ TransactionService.ts:490
fetchTransactions @ TransactionHistoryContext.tsx:184
(anonymous) @ TransactionHistoryContext.tsx:238
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:15.019Z] [TransactionService] Failed to fetch transactions {error: {…}}
log @ logger.ts:119
error @ logger.ts:155
(anonymous) @ TransactionService.ts:544
await in (anonymous)
retry @ retry.ts:124
await in retry
getTransactions @ TransactionService.ts:490
fetchTransactions @ TransactionHistoryContext.tsx:184
(anonymous) @ TransactionHistoryContext.tsx:238
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:15.020Z] [ErrorHandling] API error occurred {error: {…}}
log @ logger.ts:119
error @ logger.ts:155
handleApiError @ errorHandling.ts:119
(anonymous) @ TransactionService.ts:545
await in (anonymous)
retry @ retry.ts:124
await in retry
getTransactions @ TransactionService.ts:490
fetchTransactions @ TransactionHistoryContext.tsx:184
(anonymous) @ TransactionHistoryContext.tsx:238
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/credit_scores?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=date.asc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
DataSourceChecker.tsx:81 Database connection failed: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
checkDataSources @ DataSourceChecker.tsx:81
await in checkDataSources
(anonymous) @ DataSourceChecker.tsx:118
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
creditCards.ts:770 Error getting credit scores: Error: Failed to get credit scores: JWT expired
    at getCreditScores (creditCards.ts:765:13)
    at async refreshCreditScores (CreditScoreContext.tsx:45:20)
getCreditScores @ creditCards.ts:770
await in getCreditScores
refreshCreditScores @ CreditScoreContext.tsx:45
(anonymous) @ CreditScoreContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/categories?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=name.asc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/credit_cards?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&status=eq.active 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
CategoryContext.tsx:165 Error fetching categories: JWT expired
fetchCategories @ CategoryContext.tsx:165
await in fetchCategories
(anonymous) @ CategoryContext.tsx:311
await in (anonymous)
(anonymous) @ CategoryContext.tsx:312
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/feature_flags?select=*&order=category.asc%2Cfeature_name.asc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
investments.ts:12 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/investments?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=purchase_date.desc&offset=0&limit=20 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
setTimeout
(anonymous) @ investments.ts:12
retry @ investments.ts:12
await in retry
getInvestments @ investments.ts:43
fetchInvestments @ InvestmentContext.tsx:165
(anonymous) @ InvestmentContext.tsx:187
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
retry.ts:148 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/user_preferences?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
setTimeout
logger.error.lastError.lastError @ retry.ts:148
retry @ retry.ts:148
await in retry
getUserPreferences @ userPreferences.ts:38
(anonymous) @ useUserPreferences.ts:41
initUserPreferences @ UserPreferencesContext.tsx:51
await in initUserPreferences
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/smart_categories_preferences?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/user_preferences?select=seen_tutorials&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/credit_card_rewards?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=date.desc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
FeatureControlService.ts:75 Failed to fetch feature flags: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
getAllFeatures @ FeatureControlService.ts:75
await in getAllFeatures
loadFeatures @ FeatureControlContext.tsx:31
(anonymous) @ FeatureControlContext.tsx:23
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
investments.ts:47 Error fetching investments: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
(anonymous) @ investments.ts:47
await in (anonymous)
retry @ investments.ts:9
await in retry
getInvestments @ investments.ts:43
fetchInvestments @ InvestmentContext.tsx:165
(anonymous) @ InvestmentContext.tsx:187
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:15.161Z] [UserPreferences] Error fetching user preferences {error: {…}}
log @ logger.ts:119
error @ logger.ts:155
(anonymous) @ userPreferences.ts:52
await in (anonymous)
retry @ retry.ts:124
await in retry
getUserPreferences @ userPreferences.ts:38
(anonymous) @ useUserPreferences.ts:41
initUserPreferences @ UserPreferencesContext.tsx:51
await in initUserPreferences
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
SmartCategoriesContext.tsx:74 Error loading smart categories preferences: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
loadPreferences @ SmartCategoriesContext.tsx:74
await in loadPreferences
(anonymous) @ SmartCategoriesContext.tsx:53
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
UserGuidanceContext.tsx:389 Error loading seen tutorials: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
loadSeenTutorials @ UserGuidanceContext.tsx:389
await in loadSeenTutorials
(anonymous) @ UserGuidanceContext.tsx:393
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
creditCards.ts:869 Error getting credit card rewards: Error: Failed to get credit card rewards: JWT expired
    at getCreditCardRewards (creditCards.ts:864:13)
    at async Promise.all (index 0)
    at async refreshRewards (CreditCardRewardsContext.tsx:63:40)
getCreditCardRewards @ creditCards.ts:869
await in getCreditCardRewards
refreshRewards @ CreditCardRewardsContext.tsx:64
(anonymous) @ CreditCardRewardsContext.tsx:80
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/users?select=settings&id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/credit_cards?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=card_name.asc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/credit_scores?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=date.asc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
creditCards.ts:770 Error getting credit scores: Error: Failed to get credit scores: JWT expired
    at getCreditScores (creditCards.ts:765:13)
    at async refreshCreditScores (CreditScoreContext.tsx:45:20)
getCreditScores @ creditCards.ts:770
await in getCreditScores
refreshCreditScores @ CreditScoreContext.tsx:45
(anonymous) @ CreditScoreContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/credit_card_rewards?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=date.desc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
creditCards.ts:869 Error getting credit card rewards: Error: Failed to get credit card rewards: JWT expired
    at getCreditCardRewards (creditCards.ts:864:13)
    at async getCreditCardRewardStats (creditCards.ts:950:21)
    at async Promise.all (index 1)
getCreditCardRewards @ creditCards.ts:869
await in getCreditCardRewards
getCreditCardRewardStats @ creditCards.ts:950
refreshRewards @ CreditCardRewardsContext.tsx:65
(anonymous) @ CreditCardRewardsContext.tsx:80
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
creditCards.ts:999 Error getting reward statistics: Error: Failed to get credit card rewards: JWT expired
    at getCreditCardRewards (creditCards.ts:864:13)
    at async getCreditCardRewardStats (creditCards.ts:950:21)
    at async Promise.all (index 1)
getCreditCardRewardStats @ creditCards.ts:999
await in getCreditCardRewardStats
refreshRewards @ CreditCardRewardsContext.tsx:65
(anonymous) @ CreditCardRewardsContext.tsx:80
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/user_preferences?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
logger.ts:119 [2025-07-02T15:03:15.297Z] [UserPreferences] Error fetching user preferences {error: {…}}
log @ logger.ts:119
error @ logger.ts:155
(anonymous) @ userPreferences.ts:52
await in (anonymous)
retry @ retry.ts:124
getUserPreferences @ userPreferences.ts:38
(anonymous) @ useUserPreferences.ts:41
initUserPreferences @ UserPreferencesContext.tsx:51
await in initUserPreferences
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/users?select=settings&id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
SettingsContext.tsx:237 Error fetching settings: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
loadSettings @ SettingsContext.tsx:237
await in loadSettings
(anonymous) @ SettingsContext.tsx:275
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/user_preferences?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
logger.ts:119 [2025-07-02T15:03:15.413Z] [UserPreferences] Error fetching user preferences {error: {…}}
log @ logger.ts:119
error @ logger.ts:155
(anonymous) @ userPreferences.ts:52
await in (anonymous)
retry @ retry.ts:124
getUserPreferences @ userPreferences.ts:38
(anonymous) @ useUserPreferences.ts:41
initUserPreferences @ UserPreferencesContext.tsx:51
await in initUserPreferences
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/credit_cards?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=card_name.asc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/users?select=settings&id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
retry.ts:148 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/user_preferences?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
setTimeout
logger.error.lastError.lastError @ retry.ts:148
retry @ retry.ts:148
await in retry
getUserPreferences @ userPreferences.ts:38
(anonymous) @ useUserPreferences.ts:41
(anonymous) @ useUserPreferences.ts:115
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:15.528Z] [UserPreferences] Error fetching user preferences {error: {…}}
log @ logger.ts:119
error @ logger.ts:155
(anonymous) @ userPreferences.ts:52
await in (anonymous)
retry @ retry.ts:124
await in retry
getUserPreferences @ userPreferences.ts:38
(anonymous) @ useUserPreferences.ts:41
(anonymous) @ useUserPreferences.ts:115
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/credit_cards?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=card_name.asc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/users?select=settings&id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
SettingsContext.tsx:237 Error fetching settings: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
loadSettings @ SettingsContext.tsx:237
await in loadSettings
(anonymous) @ SettingsContext.tsx:275
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
loans.ts:14 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/loans?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=start_date.desc&offset=0&limit=20 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
setTimeout
(anonymous) @ loans.ts:14
retry @ loans.ts:14
await in retry
getLoans @ loans.ts:45
fetchLoans @ LoanContext.tsx:135
(anonymous) @ LoanContext.tsx:274
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
loans.ts:49 Error fetching loans: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
(anonymous) @ loans.ts:49
await in (anonymous)
retry @ loans.ts:11
await in retry
getLoans @ loans.ts:45
fetchLoans @ LoanContext.tsx:135
(anonymous) @ LoanContext.tsx:274
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
LoanContext.tsx:144 Error fetching loans: Error: Failed to fetch loans: JWT expired
    at loans.ts:50:13
    at async retry (loans.ts:11:14)
    at async fetchLoans (LoanContext.tsx:135:22)
fetchLoans @ LoanContext.tsx:144
await in fetchLoans
(anonymous) @ LoanContext.tsx:274
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
investments.ts:12 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/investments?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=purchase_date.desc&offset=0&limit=20 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
setTimeout
(anonymous) @ investments.ts:12
retry @ investments.ts:12
await in retry
getInvestments @ investments.ts:43
fetchInvestments @ InvestmentContext.tsx:165
(anonymous) @ InvestmentContext.tsx:187
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
investments.ts:47 Error fetching investments: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
(anonymous) @ investments.ts:47
await in (anonymous)
retry @ investments.ts:9
await in retry
getInvestments @ investments.ts:43
fetchInvestments @ InvestmentContext.tsx:165
(anonymous) @ InvestmentContext.tsx:187
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
InvestmentContext.tsx:174 Error fetching investments: Error: Failed to fetch investments: JWT expired
    at investments.ts:48:13
    at async retry (investments.ts:9:14)
    at async fetchInvestments (InvestmentContext.tsx:165:22)
fetchInvestments @ InvestmentContext.tsx:174
await in fetchInvestments
(anonymous) @ InvestmentContext.tsx:187
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
retry.ts:148 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/user_preferences?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
setTimeout
logger.error.lastError.lastError @ retry.ts:148
retry @ retry.ts:148
await in retry
getUserPreferences @ userPreferences.ts:38
(anonymous) @ useUserPreferences.ts:41
initUserPreferences @ UserPreferencesContext.tsx:51
await in initUserPreferences
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:15.925Z] [UserPreferences] Error fetching user preferences {error: {…}}
log @ logger.ts:119
error @ logger.ts:155
(anonymous) @ userPreferences.ts:52
await in (anonymous)
retry @ retry.ts:124
await in retry
getUserPreferences @ userPreferences.ts:38
(anonymous) @ useUserPreferences.ts:41
initUserPreferences @ UserPreferencesContext.tsx:51
await in initUserPreferences
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
retry.ts:148 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/user_preferences?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
setTimeout
logger.error.lastError.lastError @ retry.ts:148
retry @ retry.ts:148
await in retry
getUserPreferences @ userPreferences.ts:38
(anonymous) @ useUserPreferences.ts:41
initUserPreferences @ UserPreferencesContext.tsx:51
await in initUserPreferences
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:16.065Z] [UserPreferences] Error fetching user preferences {error: {…}}
log @ logger.ts:119
error @ logger.ts:155
(anonymous) @ userPreferences.ts:52
await in (anonymous)
retry @ retry.ts:124
await in retry
getUserPreferences @ userPreferences.ts:38
(anonymous) @ useUserPreferences.ts:41
initUserPreferences @ UserPreferencesContext.tsx:51
await in initUserPreferences
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
retry.ts:148 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/transactions?select=id%2Camount%2Ctype%2Ccategory%2Csource%2Cdate%2Cdescription%2Cmetadata%2Cattachments%2Ccreated_at%2Cupdated_at&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=date.desc&offset=0&limit=20 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
setTimeout
logger.error.lastError.lastError @ retry.ts:148
retry @ retry.ts:148
await in retry
getTransactions @ TransactionService.ts:490
fetchTransactions @ TransactionHistoryContext.tsx:184
(anonymous) @ TransactionHistoryContext.tsx:238
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
retry.ts:148 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/user_preferences?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
setTimeout
logger.error.lastError.lastError @ retry.ts:148
retry @ retry.ts:148
await in retry
getUserPreferences @ userPreferences.ts:38
(anonymous) @ useUserPreferences.ts:41
initUserPreferences @ UserPreferencesContext.tsx:51
await in initUserPreferences
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:16.145Z] [UserPreferences] Error fetching user preferences {error: {…}}
log @ logger.ts:119
error @ logger.ts:155
(anonymous) @ userPreferences.ts:52
await in (anonymous)
retry @ retry.ts:124
await in retry
getUserPreferences @ userPreferences.ts:38
(anonymous) @ useUserPreferences.ts:41
initUserPreferences @ UserPreferencesContext.tsx:51
await in initUserPreferences
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:16.146Z] All 2 retry attempts failed {lastError: {…}}
log @ logger.ts:119
error @ logger.ts:135
retry @ retry.ts:154
await in retry
getUserPreferences @ userPreferences.ts:38
(anonymous) @ useUserPreferences.ts:41
initUserPreferences @ UserPreferencesContext.tsx:51
await in initUserPreferences
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:16.146Z] [UserPreferences] Failed to get user preferences {error: {…}, userId: '468c9def-c050-49ae-b823-1dbe7bd47d40'}
log @ logger.ts:119
error @ logger.ts:155
getUserPreferences @ userPreferences.ts:59
await in getUserPreferences
(anonymous) @ useUserPreferences.ts:41
initUserPreferences @ UserPreferencesContext.tsx:51
await in initUserPreferences
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:16.547Z] [TransactionService] Error fetching transactions {error: {…}}
log @ logger.ts:119
error @ logger.ts:155
(anonymous) @ TransactionService.ts:516
await in (anonymous)
retry @ retry.ts:124
await in retry
getTransactions @ TransactionService.ts:490
fetchTransactions @ TransactionHistoryContext.tsx:184
(anonymous) @ TransactionHistoryContext.tsx:238
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:16.548Z] [TransactionService] Failed to fetch transactions {error: {…}}
log @ logger.ts:119
error @ logger.ts:155
(anonymous) @ TransactionService.ts:544
await in (anonymous)
retry @ retry.ts:124
await in retry
getTransactions @ TransactionService.ts:490
fetchTransactions @ TransactionHistoryContext.tsx:184
(anonymous) @ TransactionHistoryContext.tsx:238
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:16.549Z] [ErrorHandling] API error occurred {error: {…}}
log @ logger.ts:119
error @ logger.ts:155
handleApiError @ errorHandling.ts:119
(anonymous) @ TransactionService.ts:545
await in (anonymous)
retry @ retry.ts:124
await in retry
getTransactions @ TransactionService.ts:490
fetchTransactions @ TransactionHistoryContext.tsx:184
(anonymous) @ TransactionHistoryContext.tsx:238
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:16.556Z] All 2 retry attempts failed {lastError: Error: JWT expired
    at http://localhost:5174/src/services/TransactionService.ts:336:13
    at as…}
log @ logger.ts:119
error @ logger.ts:135
retry @ retry.ts:154
await in retry
getTransactions @ TransactionService.ts:490
fetchTransactions @ TransactionHistoryContext.tsx:184
(anonymous) @ TransactionHistoryContext.tsx:238
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:16.557Z] [TransactionHistoryContext] Error fetching transactions: {error: Error: JWT expired
    at http://localhost:5174/src/services/TransactionService.ts:336:13
    at as…}
log @ logger.ts:119
error @ logger.ts:155
fetchTransactions @ TransactionHistoryContext.tsx:209
await in fetchTransactions
(anonymous) @ TransactionHistoryContext.tsx:238
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
retry.ts:148 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/user_preferences?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
setTimeout
logger.error.lastError.lastError @ retry.ts:148
retry @ retry.ts:148
await in retry
getUserPreferences @ userPreferences.ts:38
(anonymous) @ useUserPreferences.ts:41
(anonymous) @ useUserPreferences.ts:115
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:17.233Z] [UserPreferences] Error fetching user preferences {error: {…}}
log @ logger.ts:119
error @ logger.ts:155
(anonymous) @ userPreferences.ts:52
await in (anonymous)
retry @ retry.ts:124
await in retry
getUserPreferences @ userPreferences.ts:38
(anonymous) @ useUserPreferences.ts:41
(anonymous) @ useUserPreferences.ts:115
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:17.233Z] All 2 retry attempts failed {lastError: {…}}
log @ logger.ts:119
error @ logger.ts:135
retry @ retry.ts:154
await in retry
getUserPreferences @ userPreferences.ts:38
(anonymous) @ useUserPreferences.ts:41
(anonymous) @ useUserPreferences.ts:115
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:17.233Z] [UserPreferences] Failed to get user preferences {error: {…}, userId: '468c9def-c050-49ae-b823-1dbe7bd47d40'}
log @ logger.ts:119
error @ logger.ts:155
getUserPreferences @ userPreferences.ts:59
await in getUserPreferences
(anonymous) @ useUserPreferences.ts:41
(anonymous) @ useUserPreferences.ts:115
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
retry.ts:148 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/user_preferences?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
setTimeout
logger.error.lastError.lastError @ retry.ts:148
retry @ retry.ts:148
await in retry
getUserPreferences @ userPreferences.ts:38
(anonymous) @ useUserPreferences.ts:41
initUserPreferences @ UserPreferencesContext.tsx:51
await in initUserPreferences
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:17.341Z] [UserPreferences] Error fetching user preferences {error: {…}}
log @ logger.ts:119
error @ logger.ts:155
(anonymous) @ userPreferences.ts:52
await in (anonymous)
retry @ retry.ts:124
await in retry
getUserPreferences @ userPreferences.ts:38
(anonymous) @ useUserPreferences.ts:41
initUserPreferences @ UserPreferencesContext.tsx:51
await in initUserPreferences
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:17.342Z] All 2 retry attempts failed {lastError: {…}}
log @ logger.ts:119
error @ logger.ts:135
retry @ retry.ts:154
await in retry
getUserPreferences @ userPreferences.ts:38
(anonymous) @ useUserPreferences.ts:41
initUserPreferences @ UserPreferencesContext.tsx:51
await in initUserPreferences
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:17.342Z] [UserPreferences] Failed to get user preferences {error: {…}, userId: '468c9def-c050-49ae-b823-1dbe7bd47d40'}
log @ logger.ts:119
error @ logger.ts:155
getUserPreferences @ userPreferences.ts:59
await in getUserPreferences
(anonymous) @ useUserPreferences.ts:41
initUserPreferences @ UserPreferencesContext.tsx:51
await in initUserPreferences
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
retry.ts:148 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/user_preferences?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
setTimeout
logger.error.lastError.lastError @ retry.ts:148
retry @ retry.ts:148
await in retry
getUserPreferences @ userPreferences.ts:38
(anonymous) @ useUserPreferences.ts:41
initUserPreferences @ UserPreferencesContext.tsx:51
await in initUserPreferences
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:17.410Z] [UserPreferences] Error fetching user preferences {error: {…}}
log @ logger.ts:119
error @ logger.ts:155
(anonymous) @ userPreferences.ts:52
await in (anonymous)
retry @ retry.ts:124
await in retry
getUserPreferences @ userPreferences.ts:38
(anonymous) @ useUserPreferences.ts:41
initUserPreferences @ UserPreferencesContext.tsx:51
await in initUserPreferences
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:17.410Z] All 2 retry attempts failed {lastError: {…}}
log @ logger.ts:119
error @ logger.ts:135
retry @ retry.ts:154
await in retry
getUserPreferences @ userPreferences.ts:38
(anonymous) @ useUserPreferences.ts:41
initUserPreferences @ UserPreferencesContext.tsx:51
await in initUserPreferences
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
logger.ts:119 [2025-07-02T15:03:17.410Z] [UserPreferences] Failed to get user preferences {error: {…}, userId: '468c9def-c050-49ae-b823-1dbe7bd47d40'}
log @ logger.ts:119
error @ logger.ts:155
getUserPreferences @ userPreferences.ts:59
await in getUserPreferences
(anonymous) @ useUserPreferences.ts:41
initUserPreferences @ UserPreferencesContext.tsx:51
await in initUserPreferences
(anonymous) @ UserPreferencesContext.tsx:57
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/budgets?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=category.asc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
BudgetContext.tsx:170 Error fetching budgets: Error: Failed to fetch budgets: JWT expired
    at getBudgets (budgets.ts:11:20)
    at async fetchBudgets (BudgetContext.tsx:166:20)
    at async refreshBudgets (BudgetContext.tsx:218:5)
fetchBudgets @ BudgetContext.tsx:170
await in fetchBudgets
refreshBudgets @ BudgetContext.tsx:218
callCallback2 @ react-dom.development.js:4164
invokeGuardedCallbackDev @ react-dom.development.js:4213
invokeGuardedCallback @ react-dom.development.js:4277
invokeGuardedCallbackAndCatchFirstError @ react-dom.development.js:4291
executeDispatch @ react-dom.development.js:9041
processDispatchQueueItemsInOrder @ react-dom.development.js:9073
processDispatchQueue @ react-dom.development.js:9086
dispatchEventsForPlugins @ react-dom.development.js:9097
(anonymous) @ react-dom.development.js:9288
batchedUpdates$1 @ react-dom.development.js:26179
batchedUpdates @ react-dom.development.js:3991
dispatchEventForPluginEventSystem @ react-dom.development.js:9287
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ react-dom.development.js:6465
dispatchEvent @ react-dom.development.js:6457
dispatchDiscreteEvent @ react-dom.development.js:6430
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/credit_scores?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=date.asc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/credit_scores?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&order=date.asc 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
CreditScoreTracker.tsx:53 Error fetching credit scores: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
fetchScores @ CreditScoreTracker.tsx:53
await in fetchScores
(anonymous) @ CreditScoreTracker.tsx:60
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
CreditScoreTracker.tsx:53 Error fetching credit scores: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
fetchScores @ CreditScoreTracker.tsx:53
await in fetchScores
(anonymous) @ CreditScoreTracker.tsx:60
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/credit_card_comparison_features?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
CreditCardComparison.tsx:65 Error fetching custom features: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
fetchCustomFeatures @ CreditCardComparison.tsx:65
await in fetchCustomFeatures
(anonymous) @ CreditCardComparison.tsx:69
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/credit_card_comparison_features?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
CreditCardComparison.tsx:65 Error fetching custom features: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
fetchCustomFeatures @ CreditCardComparison.tsx:65
await in fetchCustomFeatures
(anonymous) @ CreditCardComparison.tsx:69
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/telegram_users?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&is_active=eq.true 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/telegram_users?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&is_active=eq.true 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/feature_flags?select=*&feature_key=eq.telegram_integration 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/feature_flags?select=*&feature_key=eq.telegram_integration 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/user_subscriptions?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/user_subscriptions?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           POST https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/rpc/generate_telegram_auth_code 401 (Unauthorized)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
TelegramAuthService.ts:51 Database auth code generation error: {code: 'PGRST301', details: null, hint: null, message: 'JWT expired'}
generateAuthCode @ TelegramAuthService.ts:51
await in generateAuthCode
generateAuthCode @ PremiumTelegramIntegration.tsx:139
callCallback2 @ react-dom.development.js:4164
invokeGuardedCallbackDev @ react-dom.development.js:4213
invokeGuardedCallback @ react-dom.development.js:4277
invokeGuardedCallbackAndCatchFirstError @ react-dom.development.js:4291
executeDispatch @ react-dom.development.js:9041
processDispatchQueueItemsInOrder @ react-dom.development.js:9073
processDispatchQueue @ react-dom.development.js:9086
dispatchEventsForPlugins @ react-dom.development.js:9097
(anonymous) @ react-dom.development.js:9288
batchedUpdates$1 @ react-dom.development.js:26179
batchedUpdates @ react-dom.development.js:3991
dispatchEventForPluginEventSystem @ react-dom.development.js:9287
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ react-dom.development.js:6465
dispatchEvent @ react-dom.development.js:6457
dispatchDiscreteEvent @ react-dom.development.js:6430
TelegramAuthService.ts:61 Generate auth code error: Error: Failed to generate auth code
    at TelegramAuthService.generateAuthCode (TelegramAuthService.ts:52:15)
    at async generateAuthCode (PremiumTelegramIntegration.tsx:139:20)
generateAuthCode @ TelegramAuthService.ts:61
await in generateAuthCode
generateAuthCode @ PremiumTelegramIntegration.tsx:139
callCallback2 @ react-dom.development.js:4164
invokeGuardedCallbackDev @ react-dom.development.js:4213
invokeGuardedCallback @ react-dom.development.js:4277
invokeGuardedCallbackAndCatchFirstError @ react-dom.development.js:4291
executeDispatch @ react-dom.development.js:9041
processDispatchQueueItemsInOrder @ react-dom.development.js:9073
processDispatchQueue @ react-dom.development.js:9086
dispatchEventsForPlugins @ react-dom.development.js:9097
(anonymous) @ react-dom.development.js:9288
batchedUpdates$1 @ react-dom.development.js:26179
batchedUpdates @ react-dom.development.js:3991
dispatchEventForPluginEventSystem @ react-dom.development.js:9287
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ react-dom.development.js:6465
dispatchEvent @ react-dom.development.js:6457
dispatchDiscreteEvent @ react-dom.development.js:6430
PremiumTelegramIntegration.tsx:145 Failed to generate auth code: Error: Failed to generate auth code
    at TelegramAuthService.generateAuthCode (TelegramAuthService.ts:52:15)
    at async generateAuthCode (PremiumTelegramIntegration.tsx:139:20)
generateAuthCode @ PremiumTelegramIntegration.tsx:145
await in generateAuthCode
callCallback2 @ react-dom.development.js:4164
invokeGuardedCallbackDev @ react-dom.development.js:4213
invokeGuardedCallback @ react-dom.development.js:4277
invokeGuardedCallbackAndCatchFirstError @ react-dom.development.js:4291
executeDispatch @ react-dom.development.js:9041
processDispatchQueueItemsInOrder @ react-dom.development.js:9073
processDispatchQueue @ react-dom.development.js:9086
dispatchEventsForPlugins @ react-dom.development.js:9097
(anonymous) @ react-dom.development.js:9288
batchedUpdates$1 @ react-dom.development.js:26179
batchedUpdates @ react-dom.development.js:3991
dispatchEventForPluginEventSystem @ react-dom.development.js:9287
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ react-dom.development.js:6465
dispatchEvent @ react-dom.development.js:6457
dispatchDiscreteEvent @ react-dom.development.js:6430
