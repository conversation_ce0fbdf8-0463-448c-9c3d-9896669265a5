client:802 WebSocket connection to 'ws://localhost:5173/?token=L1gNoi5rgDyA' failed: 
createConnection @ client:802
connect @ client:437
connect @ client:811
connect @ client:290
connect @ client:383
(anonymous) @ client:908
client:841 [vite] failed to connect to websocket (Error: WebSocket closed without opened.). 
connect @ client:841
await in connect
connect @ client:290
connect @ client:383
(anonymous) @ client:908
client:454 Uncaught (in promise) Error: WebSocket closed without opened.
    at WebSocket.<anonymous> (client:454:22)
(anonymous) @ client:454
react-dom.development.js:86 Warning: Received `true` for a non-boolean attribute `jsx`.

If you want to write it to the DOM, pass a string instead: jsx="true" or jsx={value.toString()}.
    at style
    at AnnouncementToastManager (http://localhost:5174/src/components/user/AnnouncementToast.tsx:181:3)
    at div
    at DashboardLayout (http://localhost:5174/src/layouts/DashboardLayout.tsx:37:21)
    at RenderedRoute (http://localhost:5174/.vite_cache/deps/react-router-dom.js?v=3be051aa:4108:5)
    at Routes (http://localhost:5174/.vite_cache/deps/react-router-dom.js?v=3be051aa:4578:5)
    at AppRoutes (http://localhost:5174/src/routes.tsx:105:29)
    at div
    at NotificationProvider (http://localhost:5174/src/contexts/NotificationContext.tsx:30:40)
    at UserGuidanceProvider (http://localhost:5174/src/contexts/UserGuidanceContext.tsx:1162:40)
    at ShortcutProvider (http://localhost:5174/src/contexts/ShortcutContext.tsx:44:36)
    at SearchProvider (http://localhost:5174/src/contexts/SearchContext.tsx:29:34)
    at OfflineProvider (http://localhost:5174/src/contexts/OfflineContext.tsx:28:35)
    at UIProviders (http://localhost:5174/src/providers/UIProviders.tsx:29:31)
    at PaymentScheduleProvider (http://localhost:5174/src/contexts/PaymentScheduleContext.tsx:31:43)
    at CreditCardRewardsProvider (http://localhost:5174/src/contexts/CreditCardRewardsContext.tsx:35:45)
    at CreditScoreProvider (http://localhost:5174/src/contexts/CreditScoreContext.tsx:34:39)
    at CreditCardProvider (http://localhost:5174/src/contexts/CreditCardContext.tsx:39:38)
    at BillProvider (http://localhost:5174/src/contexts/BillContext.tsx:29:32)
    at LoanProvider (http://localhost:5174/src/contexts/LoanContext.tsx:50:32)
    at InvestmentProvider (http://localhost:5174/src/contexts/InvestmentContext.tsx:51:38)
    at BudgetProvider (http://localhost:5174/src/contexts/BudgetContext.tsx:35:34)
    at TransactionHistoryProvider (http://localhost:5174/src/contexts/TransactionHistoryContext.tsx:56:46)
    at PaymentMethodProvider (http://localhost:5174/src/contexts/PaymentMethodContext.tsx:41:41)
    at CategoryProvider (http://localhost:5174/src/contexts/CategoryContext.tsx:67:36)
    at FinancialProviders (http://localhost:5174/src/providers/FinancialProviders.tsx:35:38)
    at AccessibilityProvider (http://localhost:5174/src/components/AccessibilityProvider.tsx:42:41)
    at ErrorBoundary (http://localhost:5174/.vite_cache/deps/react-error-boundary.js?v=914cb7a3:31:5)
    at AppErrorBoundary (http://localhost:5174/src/components/ErrorBoundary.tsx:187:36)
    at Router (http://localhost:5174/.vite_cache/deps/react-router-dom.js?v=3be051aa:4521:15)
    at BrowserRouter (http://localhost:5174/.vite_cache/deps/react-router-dom.js?v=3be051aa:5267:5)
    at FeatureControlProvider (http://localhost:5174/src/contexts/FeatureControlContext.tsx:29:42)
    at SmartCategoriesProvider (http://localhost:5174/src/contexts/SmartCategoriesContext.tsx:39:43)
    at RealTimeProvider (http://localhost:5174/src/contexts/RealTimeContext.tsx:31:36)
    at UserPreferencesProvider (http://localhost:5174/src/contexts/UserPreferencesContext.tsx:32:43)
    at CurrencyProvider (http://localhost:5174/src/contexts/CurrencyContext.tsx:46:36)
    at AIServiceProvider (http://localhost:5174/src/contexts/AIServiceContext.tsx:32:37)
    at SettingsProvider (http://localhost:5174/src/contexts/SettingsContext.tsx:107:36)
    at TwoFactorProvider (http://localhost:5174/src/contexts/TwoFactorContext.tsx:31:37)
    at AuthProvider (http://localhost:5174/src/contexts/AuthContext.tsx:48:32)
    at ThemeProvider (http://localhost:5174/src/contexts/ThemeContext.tsx:27:33)
    at CoreProviders (http://localhost:5174/src/providers/CoreProviders.tsx:34:33)
    at QueryClientProvider (http://localhost:5174/.vite_cache/deps/chunk-QQM42VI5.js?v=435ffbc3:2932:3)
    at App (http://localhost:5174/src/App.tsx:54:3)
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateProperty$1 @ react-dom.development.js:3765
warnUnknownProperties @ react-dom.development.js:3803
validateProperties$2 @ react-dom.development.js:3827
validatePropertiesInDevelopment @ react-dom.development.js:9541
setInitialProperties @ react-dom.development.js:9830
finalizeInitialChildren @ react-dom.development.js:10950
completeWork @ react-dom.development.js:22232
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/telegram_users?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&is_active=eq.true 406 (Not Acceptable)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           GET https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/telegram_users?select=*&user_id=eq.468c9def-c050-49ae-b823-1dbe7bd47d40&is_active=eq.true 406 (Not Acceptable)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
fetch.ts:15 
            
            
           POST https://rprzvyfjdvjoxctawrvo.supabase.co/rest/v1/rpc/increment_feature_usage 404 (Not Found)
(anonymous) @ fetch.ts:15
(anonymous) @ fetch.ts:46
fulfilled @ fetch.ts:2
Promise.then
step @ fetch.ts:2
(anonymous) @ fetch.ts:2
__awaiter6 @ fetch.ts:2
(anonymous) @ fetch.ts:34
then @ PostgrestBuilder.ts:101
PremiumFeatureService.ts:238 Error incrementing feature usage: {code: 'PGRST202', details: 'Searched for the function public.increment_feature…r, but no matches were found in the schema cache.', hint: 'Perhaps you meant to call the function public.increment_balance', message: 'Could not find the function public.increment_feature_usage(feature_name) in the schema cache'}
recordFeatureUsage @ PremiumFeatureService.ts:238
await in recordFeatureUsage
generateAuthCode @ PremiumTelegramIntegration.tsx:143
await in generateAuthCode
callCallback2 @ react-dom.development.js:4164
invokeGuardedCallbackDev @ react-dom.development.js:4213
invokeGuardedCallback @ react-dom.development.js:4277
invokeGuardedCallbackAndCatchFirstError @ react-dom.development.js:4291
executeDispatch @ react-dom.development.js:9041
processDispatchQueueItemsInOrder @ react-dom.development.js:9073
processDispatchQueue @ react-dom.development.js:9086
dispatchEventsForPlugins @ react-dom.development.js:9097
(anonymous) @ react-dom.development.js:9288
batchedUpdates$1 @ react-dom.development.js:26179
batchedUpdates @ react-dom.development.js:3991
dispatchEventForPluginEventSystem @ react-dom.development.js:9287
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ react-dom.development.js:6465
dispatchEvent @ react-dom.development.js:6457
dispatchDiscreteEvent @ react-dom.development.js:6430
