#!/usr/bin/env node

/**
 * FiNManageR Bot Functionality Test Script
 * Tests all critical bot functions and database connections
 */

const { createClient } = require('@supabase/supabase-js');
const https = require('https');
require('dotenv').config();

const SUPABASE_URL = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function testBotFunctionality() {
  console.log('🔍 Testing FiNManageR Bot Functionality...\n');

  const tests = [];

  // Test 1: Database Connection
  try {
    const { data, error } = await supabase.from('user_permanent_auth_codes').select('count').limit(1);
    tests.push({
      name: 'Database Connection',
      status: error ? 'FAIL' : 'PASS',
      details: error ? error.message : 'Connected successfully'
    });
  } catch (error) {
    tests.push({
      name: 'Database Connection',
      status: 'FAIL',
      details: error.message
    });
  }

  // Test 2: Validate Permanent Auth Code RPC
  try {
    const { data, error } = await supabase.rpc('validate_permanent_auth_code', {
      p_auth_code: '44372203'
    });
    tests.push({
      name: 'Validate Auth Code RPC',
      status: (!error && data && data.length > 0) ? 'PASS' : 'FAIL',
      details: error ? error.message : `Valid: ${data?.[0]?.is_valid}, User: ${data?.[0]?.user_id}`
    });
  } catch (error) {
    tests.push({
      name: 'Validate Auth Code RPC',
      status: 'FAIL',
      details: error.message
    });
  }

  // Test 3: Get/Create Permanent Auth Code RPC
  try {
    const { data, error } = await supabase.rpc('get_or_create_permanent_auth_code', {
      p_user_id: '468c9def-c050-49ae-b823-1dbe7bd47d40'
    });
    tests.push({
      name: 'Get/Create Auth Code RPC',
      status: (!error && data && data.length > 0) ? 'PASS' : 'FAIL',
      details: error ? error.message : `Code: ${data?.[0]?.auth_code}, Usage: ${data?.[0]?.usage_count}`
    });
  } catch (error) {
    tests.push({
      name: 'Get/Create Auth Code RPC',
      status: 'FAIL',
      details: error.message
    });
  }

  // Test 4: Link Telegram Account RPC
  try {
    const { data, error } = await supabase.rpc('link_telegram_account', {
      p_telegram_id: *********,
      p_user_id: '468c9def-c050-49ae-b823-1dbe7bd47d40',
      p_username: 'TestUser',
      p_first_name: 'Test',
      p_last_name: 'User',
      p_language_code: 'en'
    });
    tests.push({
      name: 'Link Telegram Account RPC',
      status: (!error && data) ? 'PASS' : 'FAIL',
      details: error ? error.message : `Link successful: ${data}`
    });
  } catch (error) {
    tests.push({
      name: 'Link Telegram Account RPC',
      status: 'FAIL',
      details: error.message
    });
  }

  // Test 5: Bot Health Check
  try {
    const healthData = await new Promise((resolve, reject) => {
      const req = https.get('http://localhost:3002/health', (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            resolve(JSON.parse(data));
          } catch (e) {
            reject(e);
          }
        });
      });
      req.on('error', reject);
      req.setTimeout(5000, () => reject(new Error('Timeout')));
    });

    tests.push({
      name: 'Bot Health Check',
      status: (healthData.status === 'healthy') ? 'PASS' : 'FAIL',
      details: `Status: ${healthData.status}, Features: ${Object.keys(healthData.features || {}).length}`
    });
  } catch (error) {
    tests.push({
      name: 'Bot Health Check',
      status: 'FAIL',
      details: 'Bot not running or health endpoint unavailable'
    });
  }

  // Test 6: RLS Policies Check
  try {
    const { data, error } = await supabase.rpc('exec', {
      sql: `SELECT tablename, rowsecurity FROM pg_tables WHERE schemaname = 'public' AND tablename IN ('telegram_users', 'user_permanent_auth_codes') ORDER BY tablename;`
    });
    tests.push({
      name: 'RLS Policies Check',
      status: (!error && data) ? 'PASS' : 'FAIL',
      details: error ? error.message : `RLS enabled on ${data?.length || 0} tables`
    });
  } catch (error) {
    tests.push({
      name: 'RLS Policies Check',
      status: 'FAIL',
      details: error.message
    });
  }

  // Display Results
  console.log('📊 TEST RESULTS:\n');
  console.log('┌─────────────────────────────────┬────────┬─────────────────────────────────────────┐');
  console.log('│ Test Name                       │ Status │ Details                                 │');
  console.log('├─────────────────────────────────┼────────┼─────────────────────────────────────────┤');
  
  tests.forEach(test => {
    const name = test.name.padEnd(31);
    const status = test.status === 'PASS' ? '✅ PASS' : '❌ FAIL';
    const statusPadded = status.padEnd(6);
    const details = test.details.length > 39 ? test.details.substring(0, 36) + '...' : test.details.padEnd(39);
    console.log(`│ ${name} │ ${statusPadded} │ ${details} │`);
  });
  
  console.log('└─────────────────────────────────┴────────┴─────────────────────────────────────────┘\n');

  // Summary
  const passCount = tests.filter(t => t.status === 'PASS').length;
  const totalCount = tests.length;
  const successRate = Math.round((passCount / totalCount) * 100);

  console.log(`📈 SUMMARY: ${passCount}/${totalCount} tests passed (${successRate}%)`);
  
  if (successRate === 100) {
    console.log('🎉 ALL TESTS PASSED! Bot is fully functional and ready for use.');
    console.log('\n🔗 Ready to test with Telegram bot @Myfnmbot');
    console.log('📱 Use command: /link 44372203');
  } else {
    console.log('⚠️  Some tests failed. Please check the details above.');
  }

  console.log('\n🏥 Bot Health: http://localhost:3002/health');
  console.log('🌐 Web App: http://localhost:5174/settings/telegram');
}

// Run tests
testBotFunctionality().catch(console.error);
