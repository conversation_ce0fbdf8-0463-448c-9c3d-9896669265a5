#!/usr/bin/env node

/**
 * Development Environment Setup Script
 * Ensures optimal development environment configuration
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Setting up development environment...');

// 1. Check and create necessary directories
const directories = [
  '.vite_cache',
  'supabase/migrations',
  'logs',
  '.bolt'
];

directories.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`✅ Created directory: ${dir}`);
  }
});

// 2. Create or update .env.local with development settings
const envLocalPath = '.env.local';
const envLocalContent = `# Development Environment Settings
# Auto-generated by dev-environment-setup.js

# Vite Development Server
VITE_DEV_SERVER_PORT=5174
VITE_DEV_SERVER_HOST=localhost

# Development Flags
NODE_ENV=development
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_CONSOLE_LOGS=true

# Performance Settings
VITE_CACHE_DIR=.vite_cache
VITE_FORCE_OPTIMIZE_DEPS=true

# Error Handling
VITE_ENHANCED_ERROR_HANDLING=true
VITE_DEBOUNCE_ERRORS=true
`;

// Only create if it doesn't exist to avoid overwriting user settings
if (!fs.existsSync(envLocalPath)) {
  fs.writeFileSync(envLocalPath, envLocalContent);
  console.log('✅ Created .env.local with development settings');
} else {
  console.log('ℹ️  .env.local already exists, skipping...');
}

// 3. Create package.json scripts for development
const packageJsonPath = 'package.json';
if (fs.existsSync(packageJsonPath)) {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // Add development scripts if they don't exist
  const devScripts = {
    'dev:clean': 'rm -rf .vite_cache && npm run dev',
    'dev:debug': 'DEBUG=vite:* npm run dev',
    'dev:host': 'npm run dev -- --host',
    'fix:permissions': 'chmod +x scripts/*.js',
    'db:migrate': 'echo "Please run the migration in Supabase SQL Editor"',
    'db:reset': 'echo "Please reset database in Supabase Dashboard"',
    'logs:clear': 'rm -rf logs/* && echo "Logs cleared"',
    'cache:clear': 'rm -rf .vite_cache node_modules/.cache && echo "Cache cleared"'
  };

  let scriptsAdded = false;
  Object.entries(devScripts).forEach(([key, value]) => {
    if (!packageJson.scripts[key]) {
      packageJson.scripts[key] = value;
      scriptsAdded = true;
    }
  });

  if (scriptsAdded) {
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    console.log('✅ Added development scripts to package.json');
  }
}

// 4. Create development configuration file
const devConfigPath = 'dev.config.js';
const devConfigContent = `// Development Configuration
// Auto-generated by dev-environment-setup.js

module.exports = {
  // Server settings
  server: {
    port: 5174,
    host: 'localhost',
    strictPort: false,
    clearScreen: false
  },
  
  // Error handling
  errorHandling: {
    enhanced: true,
    debounce: true,
    logLevel: 'warn'
  },
  
  // Performance
  performance: {
    cacheDir: '.vite_cache',
    forceOptimizeDeps: true,
    clearCacheOnStart: false
  },
  
  // Database
  database: {
    retryAttempts: 3,
    timeout: 5000,
    silentErrors: ['PGRST116', 'PGRST301']
  }
};
`;

fs.writeFileSync(devConfigPath, devConfigContent);
console.log('✅ Created dev.config.js');

// 5. Create a simple health check script
const healthCheckPath = 'scripts/health-check.js';
const healthCheckContent = `#!/usr/bin/env node

/**
 * Development Health Check
 * Verifies that all systems are working correctly
 */

const http = require('http');
const fs = require('fs');

console.log('🏥 Running development health check...');

// Check if server is running
const checkServer = () => {
  return new Promise((resolve) => {
    const req = http.get('http://localhost:5174', (res) => {
      resolve(res.statusCode === 200);
    });
    
    req.on('error', () => resolve(false));
    req.setTimeout(2000, () => {
      req.destroy();
      resolve(false);
    });
  });
};

// Check file permissions
const checkPermissions = () => {
  try {
    fs.accessSync('.', fs.constants.R_OK | fs.constants.W_OK);
    return true;
  } catch {
    return false;
  }
};

// Check required directories
const checkDirectories = () => {
  const required = ['.vite_cache', 'src', 'public'];
  return required.every(dir => fs.existsSync(dir));
};

async function runHealthCheck() {
  const checks = {
    'File Permissions': checkPermissions(),
    'Required Directories': checkDirectories(),
    'Development Server': await checkServer()
  };

  console.log('\\n📊 Health Check Results:');
  Object.entries(checks).forEach(([name, status]) => {
    console.log(\`\${status ? '✅' : '❌'} \${name}\`);
  });

  const allPassed = Object.values(checks).every(Boolean);
  console.log(\`\\n\${allPassed ? '🎉 All checks passed!' : '⚠️  Some checks failed'}\`);
  
  if (!allPassed) {
    console.log('\\n💡 Suggestions:');
    if (!checks['Development Server']) {
      console.log('   - Run: npm run dev');
    }
    if (!checks['File Permissions']) {
      console.log('   - Run: npm run fix:permissions');
    }
    if (!checks['Required Directories']) {
      console.log('   - Run: npm run dev:clean');
    }
  }
}

runHealthCheck().catch(console.error);
`;

if (!fs.existsSync('scripts')) {
  fs.mkdirSync('scripts', { recursive: true });
}

fs.writeFileSync(healthCheckPath, healthCheckContent);
fs.chmodSync(healthCheckPath, '755');
console.log('✅ Created health check script');

// 6. Create gitignore entries for development files
const gitignorePath = '.gitignore';
const devGitignoreEntries = [
  '',
  '# Development Environment',
  '.vite_cache/',
  'dev.config.js',
  'logs/',
  '.env.local',
  '*.log',
  '.DS_Store',
  'Thumbs.db'
];

if (fs.existsSync(gitignorePath)) {
  const gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
  const entriesToAdd = devGitignoreEntries.filter(entry => 
    entry === '' || !gitignoreContent.includes(entry)
  );
  
  if (entriesToAdd.length > 0) {
    fs.appendFileSync(gitignorePath, '\\n' + entriesToAdd.join('\\n'));
    console.log('✅ Updated .gitignore with development entries');
  }
}

console.log('\\n🎉 Development environment setup complete!');
console.log('\\n📝 Available commands:');
console.log('   npm run dev:clean    - Clean cache and start dev server');
console.log('   npm run dev:debug    - Start dev server with debug logs');
console.log('   npm run health-check - Run system health check');
console.log('   npm run cache:clear  - Clear all caches');
console.log('\\n🚀 Ready to start development!');
