/**
 * Database Service
 * 
 * Handles all database operations using Supabase
 */

import { createClient } from '@supabase/supabase-js';
import { logger } from '../utils/logger.js';

export class DatabaseService {
  constructor(config) {
    this.config = config;
    this.supabase = null;
    this.isInitialized = false;
  }

  async initialize() {
    try {
      logger.info('🔧 Initializing database service...');

      this.supabase = createClient(
        this.config.SUPABASE_URL,
        this.config.SUPABASE_SERVICE_ROLE_KEY,
        {
          auth: {
            autoRefreshToken: true,
            persistSession: false,
            storageKey: 'finmanager-telegram-service-auth'
          },
          global: {
            headers: {
              'x-application-name': 'financial-management-telegram-service',
              'X-Client-Info': 'finmanager-telegram-service'
            }
          }
        }
      );

      // Test connection
      const { data, error } = await this.supabase
        .from('users')
        .select('count')
        .limit(1);

      if (error) {
        throw new Error(`Database connection failed: ${error.message}`);
      }

      this.isInitialized = true;
      logger.info('✅ Database service initialized');

    } catch (error) {
      logger.error('❌ Database initialization failed:', error);
      throw error;
    }
  }

  async close() {
    // Supabase client doesn't need explicit closing
    this.isInitialized = false;
    logger.info('✅ Database service closed');
  }

  // User management
  async getUserByTelegramId(telegramId) {
    // First check telegram_users table for the link
    const { data: telegramUser, error: telegramError } = await this.supabase
      .from('telegram_users')
      .select('user_id')
      .eq('telegram_id', telegramId)
      .eq('is_active', true)
      .single();

    if (telegramError && telegramError.code !== 'PGRST116') {
      throw telegramError;
    }

    if (!telegramUser) {
      return null; // No telegram user found
    }

    // Get the actual user data
    const { data, error } = await this.supabase
      .from('users')
      .select('*')
      .eq('id', telegramUser.user_id)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw error;
    }

    return data;
  }

  async createUser(userData) {
    // This method should create a user in the users table
    // Note: In our system, users are created via Supabase Auth
    // This method is mainly for linking Telegram users
    const { data, error } = await this.supabase
      .from('users')
      .insert(userData)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  }

  // Transaction management
  async addTransaction(userId, transactionData) {
    const { data, error } = await this.supabase
      .from('transactions')
      .insert({
        user_id: userId,
        ...transactionData
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  }

  async getRecentTransactions(userId, limit = 10) {
    const { data, error } = await this.supabase
      .from('transactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      throw error;
    }

    return data;
  }

  // Account linking
  async createLinkCode(userId, code) {
    const { data, error } = await this.supabase
      .from('telegram_link_codes')
      .insert({
        user_id: userId,
        code,
        expires_at: new Date(Date.now() + 5 * 60 * 1000) // 5 minutes
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  }

  async validateLinkCode(code) {
    try {
      // Use the new permanent auth code validation RPC function
      const { data, error } = await this.supabase.rpc('validate_permanent_auth_code', {
        p_auth_code: code
      });

      if (error) {
        console.error('Error validating permanent auth code:', error);
        return null;
      }

      if (!data || data.length === 0 || !data[0].is_valid) {
        console.log('Invalid or expired auth code:', code);
        return null;
      }

      // Return the user_id in the expected format
      return { user_id: data[0].user_id };
    } catch (error) {
      console.error('Validate permanent auth code error:', error);
      return null;
    }
  }

  async linkTelegramAccount(userId, telegramId, telegramUserInfo) {
    try {
      // Use the RPC function to link the account
      const { data, error } = await this.supabase.rpc('link_telegram_account', {
        p_telegram_id: parseInt(telegramId),
        p_user_id: userId,
        p_username: telegramUserInfo?.username || null,
        p_first_name: telegramUserInfo?.first_name || null,
        p_last_name: telegramUserInfo?.last_name || null,
        p_language_code: 'en'
      });

      if (error) {
        console.error('Error linking telegram account:', error);
        throw new Error('Failed to link Telegram account');
      }

      return data;
    } catch (error) {
      console.error('Link telegram account error:', error);
      throw error;
    }
  }
}
