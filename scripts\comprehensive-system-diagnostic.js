#!/usr/bin/env node

/**
 * Comprehensive End-to-End System Diagnostic
 * Tests all components of the FiNManageR Telegram integration
 */

import { createClient } from '@supabase/supabase-js';
import fetch from 'node-fetch';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const SUPABASE_URL = 'https://rprzvyfjdvjoxctawrvo.supabase.co';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;
const WEB_APP_URL = 'http://localhost:5175';
const BOT_HEALTH_URL = 'http://localhost:3001/health';

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

class SystemDiagnostic {
  constructor() {
    this.results = {
      database: {},
      webApp: {},
      bot: {},
      integration: {},
      overall: 'PENDING'
    };
  }

  async runDiagnostics() {
    console.log('🔍 Starting Comprehensive System Diagnostics...\n');
    
    try {
      await this.testDatabase();
      await this.testWebApplication();
      await this.testTelegramBot();
      await this.testIntegration();
      
      this.generateReport();
    } catch (error) {
      console.error('❌ Diagnostic failed:', error);
      this.results.overall = 'FAILED';
    }
  }

  async testDatabase() {
    console.log('📊 Testing Database Layer...');
    
    try {
      // Test 1: Check all required tables exist
      const { data: tables, error: tablesError } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .in('table_name', ['telegram_users', 'telegram_auth_codes', 'notifications', 'feature_flags'])
        .eq('table_schema', 'public');

      this.results.database.tables = tablesError ? 'FAILED' : 'PASSED';
      
      // Test 2: Test RPC functions
      const testUserId = '716109b5-f7bc-422e-8f67-09689ec825ef';
      
      // Generate auth code
      const { data: authCode, error: authError } = await supabase.rpc('generate_telegram_auth_code', {
        p_user_id: testUserId
      });
      this.results.database.authCodeGeneration = authError ? 'FAILED' : 'PASSED';
      
      if (authCode) {
        // Verify auth code
        const { data: verifiedUserId, error: verifyError } = await supabase.rpc('verify_telegram_auth_code', {
          p_code: authCode
        });
        this.results.database.authCodeVerification = verifyError ? 'FAILED' : 'PASSED';
        
        // Test account linking
        const { data: linkedId, error: linkError } = await supabase.rpc('link_telegram_account', {
          p_telegram_id: *********,
          p_user_id: testUserId,
          p_username: 'diagnostic_test',
          p_first_name: 'Test',
          p_last_name: 'User'
        });
        this.results.database.accountLinking = linkError ? 'FAILED' : 'PASSED';
      }
      
      // Test feature usage increment
      const { error: featureError } = await supabase.rpc('increment_feature_usage', {
        feature_name: 'diagnostic_test'
      });
      this.results.database.featureUsage = featureError ? 'FAILED' : 'PASSED';
      
      // Test notification count
      const { data: notificationCount, error: notificationError } = await supabase.rpc('get_unread_notification_count', {
        p_user_id: testUserId
      });
      this.results.database.notificationCount = notificationError ? 'FAILED' : 'PASSED';
      
      console.log('✅ Database tests completed');
      
    } catch (error) {
      console.error('❌ Database test failed:', error);
      this.results.database.overall = 'FAILED';
    }
  }

  async testWebApplication() {
    console.log('🌐 Testing Web Application...');
    
    try {
      // Test 1: Check if web app is running
      const response = await fetch(WEB_APP_URL, { timeout: 5000 });
      this.results.webApp.server = response.ok ? 'PASSED' : 'FAILED';
      
      // Test 2: Check Telegram settings page
      const telegramPageResponse = await fetch(`${WEB_APP_URL}/settings/telegram`, { timeout: 5000 });
      this.results.webApp.telegramPage = telegramPageResponse.ok ? 'PASSED' : 'FAILED';
      
      console.log('✅ Web application tests completed');
      
    } catch (error) {
      console.error('❌ Web application test failed:', error);
      this.results.webApp.overall = 'FAILED';
    }
  }

  async testTelegramBot() {
    console.log('🤖 Testing Telegram Bot...');
    
    try {
      // Test 1: Check bot health endpoint
      const healthResponse = await fetch(BOT_HEALTH_URL, { timeout: 5000 });
      this.results.bot.health = healthResponse.ok ? 'PASSED' : 'FAILED';
      
      if (healthResponse.ok) {
        const healthData = await healthResponse.json();
        this.results.bot.status = healthData.status === 'healthy' ? 'PASSED' : 'FAILED';
      }
      
      console.log('✅ Telegram bot tests completed');
      
    } catch (error) {
      console.error('❌ Telegram bot test failed:', error);
      this.results.bot.overall = 'FAILED';
    }
  }

  async testIntegration() {
    console.log('🔗 Testing End-to-End Integration...');
    
    try {
      // Test complete flow: Generate code -> Verify -> Link
      const testUserId = '716109b5-f7bc-422e-8f67-09689ec825ef';
      
      // Step 1: Generate auth code
      const { data: authCode, error: genError } = await supabase.rpc('generate_telegram_auth_code', {
        p_user_id: testUserId
      });
      
      if (genError || !authCode) {
        this.results.integration.codeGeneration = 'FAILED';
        return;
      }
      
      this.results.integration.codeGeneration = 'PASSED';
      
      // Step 2: Verify auth code
      const { data: verifiedUserId, error: verifyError } = await supabase.rpc('verify_telegram_auth_code', {
        p_code: authCode
      });
      
      this.results.integration.codeVerification = (verifyError || verifiedUserId !== testUserId) ? 'FAILED' : 'PASSED';
      
      // Step 3: Test account linking
      const { data: linkedId, error: linkError } = await supabase.rpc('link_telegram_account', {
        p_telegram_id: *********,
        p_user_id: testUserId,
        p_username: 'integration_test',
        p_first_name: 'Integration',
        p_last_name: 'Test'
      });
      
      this.results.integration.accountLinking = linkError ? 'FAILED' : 'PASSED';
      
      console.log('✅ Integration tests completed');
      
    } catch (error) {
      console.error('❌ Integration test failed:', error);
      this.results.integration.overall = 'FAILED';
    }
  }

  generateReport() {
    console.log('\n📋 COMPREHENSIVE DIAGNOSTIC REPORT');
    console.log('=====================================\n');
    
    // Database Results
    console.log('📊 DATABASE LAYER:');
    console.log(`   Tables: ${this.getStatusIcon(this.results.database.tables)} ${this.results.database.tables || 'NOT_TESTED'}`);
    console.log(`   Auth Code Generation: ${this.getStatusIcon(this.results.database.authCodeGeneration)} ${this.results.database.authCodeGeneration || 'NOT_TESTED'}`);
    console.log(`   Auth Code Verification: ${this.getStatusIcon(this.results.database.authCodeVerification)} ${this.results.database.authCodeVerification || 'NOT_TESTED'}`);
    console.log(`   Account Linking: ${this.getStatusIcon(this.results.database.accountLinking)} ${this.results.database.accountLinking || 'NOT_TESTED'}`);
    console.log(`   Feature Usage: ${this.getStatusIcon(this.results.database.featureUsage)} ${this.results.database.featureUsage || 'NOT_TESTED'}`);
    console.log(`   Notification Count: ${this.getStatusIcon(this.results.database.notificationCount)} ${this.results.database.notificationCount || 'NOT_TESTED'}\n`);
    
    // Web App Results
    console.log('🌐 WEB APPLICATION:');
    console.log(`   Server Running: ${this.getStatusIcon(this.results.webApp.server)} ${this.results.webApp.server || 'NOT_TESTED'}`);
    console.log(`   Telegram Page: ${this.getStatusIcon(this.results.webApp.telegramPage)} ${this.results.webApp.telegramPage || 'NOT_TESTED'}\n`);
    
    // Bot Results
    console.log('🤖 TELEGRAM BOT:');
    console.log(`   Health Endpoint: ${this.getStatusIcon(this.results.bot.health)} ${this.results.bot.health || 'NOT_TESTED'}`);
    console.log(`   Bot Status: ${this.getStatusIcon(this.results.bot.status)} ${this.results.bot.status || 'NOT_TESTED'}\n`);
    
    // Integration Results
    console.log('🔗 END-TO-END INTEGRATION:');
    console.log(`   Code Generation: ${this.getStatusIcon(this.results.integration.codeGeneration)} ${this.results.integration.codeGeneration || 'NOT_TESTED'}`);
    console.log(`   Code Verification: ${this.getStatusIcon(this.results.integration.codeVerification)} ${this.results.integration.codeVerification || 'NOT_TESTED'}`);
    console.log(`   Account Linking: ${this.getStatusIcon(this.results.integration.accountLinking)} ${this.results.integration.accountLinking || 'NOT_TESTED'}\n`);
    
    // Overall Status
    const allPassed = this.checkOverallStatus();
    this.results.overall = allPassed ? 'PASSED' : 'FAILED';
    
    console.log('🎯 OVERALL SYSTEM STATUS:');
    console.log(`   ${this.getStatusIcon(this.results.overall)} ${this.results.overall}\n`);
    
    if (allPassed) {
      console.log('🎉 ALL SYSTEMS OPERATIONAL! Telegram integration is ready for use.');
    } else {
      console.log('⚠️  ISSUES DETECTED! Please review failed tests above.');
    }
  }

  getStatusIcon(status) {
    switch (status) {
      case 'PASSED': return '✅';
      case 'FAILED': return '❌';
      default: return '⏳';
    }
  }

  checkOverallStatus() {
    const allResults = [
      ...Object.values(this.results.database),
      ...Object.values(this.results.webApp),
      ...Object.values(this.results.bot),
      ...Object.values(this.results.integration)
    ];
    
    return allResults.every(result => result === 'PASSED' || result === undefined);
  }
}

// Run diagnostics
const diagnostic = new SystemDiagnostic();
diagnostic.runDiagnostics().catch(console.error);
