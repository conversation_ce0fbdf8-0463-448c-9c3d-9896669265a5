#!/usr/bin/env node

/**
 * Development Health Check
 * Verifies that all systems are working correctly
 */

import fs from 'fs';
import path from 'path';

console.log('🏥 Running development health check...');

// Check required directories
const checkDirectories = () => {
  const required = [
    { path: 'src', description: 'Source code directory' },
    { path: 'public', description: 'Public assets directory' },
    { path: 'supabase', description: 'Supabase configuration' },
    { path: '.vite_cache', description: 'Vite cache directory', create: true }
  ];
  
  const results = [];
  
  required.forEach(({ path: dirPath, description, create }) => {
    const exists = fs.existsSync(dirPath);
    
    if (!exists && create) {
      try {
        fs.mkdirSync(dirPath, { recursive: true });
        results.push({ name: description, status: true, action: 'created' });
      } catch (error) {
        results.push({ name: description, status: false, error: error.message });
      }
    } else {
      results.push({ name: description, status: exists });
    }
  });
  
  return results;
};

// Check file permissions
const checkPermissions = () => {
  const results = [];
  
  try {
    fs.accessSync('.', fs.constants.R_OK | fs.constants.W_OK);
    results.push({ name: 'Project directory permissions', status: true });
  } catch (error) {
    results.push({ name: 'Project directory permissions', status: false, error: error.message });
  }
  
  // Check if we can write to important files
  const importantFiles = ['package.json', 'vite.config.ts', '.env'];
  
  importantFiles.forEach(file => {
    if (fs.existsSync(file)) {
      try {
        fs.accessSync(file, fs.constants.R_OK | fs.constants.W_OK);
        results.push({ name: `${file} permissions`, status: true });
      } catch (error) {
        results.push({ name: `${file} permissions`, status: false, error: error.message });
      }
    }
  });
  
  return results;
};

// Check environment configuration
const checkEnvironment = () => {
  const results = [];
  
  // Check if .env exists
  const envExists = fs.existsSync('.env');
  results.push({ 
    name: 'Environment file (.env)', 
    status: envExists,
    suggestion: envExists ? null : 'Create .env file with your Supabase credentials'
  });
  
  // Check Node.js version
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  results.push({ 
    name: `Node.js version (${nodeVersion})`, 
    status: majorVersion >= 16,
    suggestion: majorVersion < 16 ? 'Upgrade to Node.js 16 or higher' : null
  });
  
  return results;
};

// Check package.json scripts
const checkScripts = () => {
  const results = [];
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const requiredScripts = ['dev', 'build', 'preview'];
    
    requiredScripts.forEach(script => {
      const exists = packageJson.scripts && packageJson.scripts[script];
      results.push({ 
        name: `Script: ${script}`, 
        status: !!exists,
        suggestion: exists ? null : `Add "${script}" script to package.json`
      });
    });
    
  } catch (error) {
    results.push({ name: 'package.json parsing', status: false, error: error.message });
  }
  
  return results;
};

// Check for common issues
const checkCommonIssues = () => {
  const results = [];
  
  // Check for node_modules
  const nodeModulesExists = fs.existsSync('node_modules');
  results.push({ 
    name: 'Dependencies installed', 
    status: nodeModulesExists,
    suggestion: nodeModulesExists ? null : 'Run: npm install'
  });
  
  // Check for lock file
  const lockFileExists = fs.existsSync('package-lock.json') || fs.existsSync('yarn.lock') || fs.existsSync('pnpm-lock.yaml');
  results.push({ 
    name: 'Lock file present', 
    status: lockFileExists,
    suggestion: lockFileExists ? null : 'Run: npm install to generate lock file'
  });
  
  return results;
};

async function runHealthCheck() {
  const checkSections = [
    { name: 'Directories', checks: checkDirectories() },
    { name: 'Permissions', checks: checkPermissions() },
    { name: 'Environment', checks: checkEnvironment() },
    { name: 'Scripts', checks: checkScripts() },
    { name: 'Dependencies', checks: checkCommonIssues() }
  ];

  console.log('\n📊 Health Check Results:\n');
  
  let allPassed = true;
  const suggestions = [];

  checkSections.forEach(({ name, checks }) => {
    console.log(`\n🔍 ${name}:`);
    
    checks.forEach(check => {
      const icon = check.status ? '✅' : '❌';
      const action = check.action ? ` (${check.action})` : '';
      console.log(`   ${icon} ${check.name}${action}`);
      
      if (!check.status) {
        allPassed = false;
        if (check.error) {
          console.log(`      Error: ${check.error}`);
        }
        if (check.suggestion) {
          suggestions.push(check.suggestion);
        }
      }
    });
  });

  console.log(`\n${allPassed ? '🎉 All checks passed!' : '⚠️  Some checks failed'}`);
  
  if (suggestions.length > 0) {
    console.log('\n💡 Suggestions:');
    suggestions.forEach(suggestion => {
      console.log(`   - ${suggestion}`);
    });
  }
  
  if (allPassed) {
    console.log('\n🚀 Your development environment is ready!');
    console.log('\n📝 Available commands:');
    console.log('   npm run dev        - Start development server');
    console.log('   npm run dev:clean  - Clean cache and start dev server');
    console.log('   npm run build      - Build for production');
    console.log('   npm run cache:clear - Clear all caches');
  }
  
  return allPassed;
}

// Run the health check
runHealthCheck().catch(error => {
  console.error('❌ Health check failed:', error);
  process.exit(1);
});
