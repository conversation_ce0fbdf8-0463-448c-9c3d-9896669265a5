import { supabase } from '../lib/supabase';
import { Database } from '../lib/database.types';

export interface TelegramAuthCode {
  id: string;
  code: string;
  user_id: string;
  expires_at: string;
  used_at?: string;
  is_used: boolean;
  created_at: string;
}

export interface TelegramUser {
  id: string;
  telegram_id: string;
  user_id: string;
  username?: string;
  first_name?: string;
  last_name?: string;
  language_code: string;
  linked_at: string;
  is_active: boolean;
  preferences: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface TelegramSession {
  id: string;
  telegram_user_id: string;
  session_token: string;
  expires_at: string;
  is_active: boolean;
  last_activity: string;
  created_at: string;
}

export class TelegramAuthService {
  /**
   * Generate a secure auth code for account linking
   */
  async generateAuthCode(userId: string): Promise<string> {
    try {
      // Use database function to generate auth code
      const { data: authCode, error } = await supabase.rpc('generate_telegram_auth_code', {
        p_user_id: userId
      });

      if (error) {
        console.error('Database auth code generation error:', error);
        throw new Error('Failed to generate auth code');
      }

      if (!authCode) {
        throw new Error('No auth code returned from database');
      }

      return authCode as string;
    } catch (error) {
      console.error('Generate auth code error:', error);
      throw error;
    }
  }

  /**
   * Verify an auth code and return the associated user ID
   */
  async verifyAuthCode(code: string): Promise<string | null> {
    try {
      const { data, error } = await supabase.rpc('verify_telegram_auth_code', {
        p_code: code.toUpperCase()
      });

      if (error) {
        console.error('Error verifying auth code:', error);
        return null;
      }

      return data as string | null;
    } catch (error) {
      console.error('Verify auth code error:', error);
      return null;
    }
  }

  /**
   * Link a Telegram account to a FiNManageR user
   */
  async linkTelegramAccount(
    telegramId: string,
    userId: string,
    telegramUserInfo?: {
      username?: string;
      first_name?: string;
      last_name?: string;
      language_code?: string;
    }
  ): Promise<TelegramUser | null> {
    try {
      const { data, error } = await supabase.rpc('link_telegram_account', {
        p_telegram_id: parseInt(telegramId),
        p_user_id: userId,
        p_username: telegramUserInfo?.username || null,
        p_first_name: telegramUserInfo?.first_name || null,
        p_last_name: telegramUserInfo?.last_name || null,
        p_language_code: telegramUserInfo?.language_code || 'en'
      });

      if (error) {
        console.error('Error linking telegram account:', error);
        throw new Error('Failed to link Telegram account');
      }

      // Fetch the created telegram user
      const telegramUser = await this.getTelegramUserById(data);
      return telegramUser;
    } catch (error) {
      console.error('Link telegram account error:', error);
      throw error;
    }
  }

  /**
   * Unlink a Telegram account
   */
  async unlinkTelegramAccount(telegramId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase.rpc('unlink_telegram_account', {
        p_telegram_id: parseInt(telegramId)
      });

      if (error) {
        console.error('Error unlinking telegram account:', error);
        return false;
      }

      return data as boolean;
    } catch (error) {
      console.error('Unlink telegram account error:', error);
      return false;
    }
  }

  /**
   * Get Telegram user by Telegram ID
   */
  async getTelegramUser(telegramId: string): Promise<TelegramUser | null> {
    try {
      const { data, error } = await supabase
        .from('telegram_users')
        .select('*')
        .eq('telegram_id', telegramId)
        .eq('is_active', true)
        .single();

      if (error) {
        // Handle specific error cases
        if (error.code === 'PGRST116') {
          // No rows found - this is expected, not an error
          return null;
        } else if (error.code === 'PGRST301' || error.message?.includes('JWT expired')) {
          console.warn('Authentication expired while fetching Telegram user');
          return null;
        } else if (error.code === 'PGRST202' || error.message?.includes('Not Acceptable')) {
          console.warn('Telegram users table may not exist or have incorrect schema:', error.message);
          return null;
        } else {
          console.error('Error fetching Telegram user:', error);
          return null;
        }
      }

      if (!data) {
        return null;
      }

      return data as TelegramUser;
    } catch (error) {
      console.error('Get telegram user error:', error);
      return null;
    }
  }

  /**
   * Get Telegram user by ID
   */
  async getTelegramUserById(id: string): Promise<TelegramUser | null> {
    try {
      const { data, error } = await supabase
        .from('telegram_users')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        // Handle specific error cases silently for expected errors
        if (error.code === 'PGRST116') {
          return null; // No rows found
        } else if (error.code === 'PGRST301' || error.message?.includes('JWT expired')) {
          console.warn('Authentication expired while fetching Telegram user by ID');
          return null;
        } else if (error.code === 'PGRST202' || error.message?.includes('Not Acceptable')) {
          console.warn('Telegram users table schema issue:', error.message);
          return null;
        } else {
          console.error('Error fetching Telegram user by ID:', error);
          return null;
        }
      }

      if (!data) {
        return null;
      }

      return data as TelegramUser;
    } catch (error) {
      console.error('Get telegram user by id error:', error);
      return null;
    }
  }

  /**
   * Get Telegram user by FiNManageR user ID
   */
  async getTelegramUserByUserId(userId: string): Promise<TelegramUser | null> {
    try {
      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Database query timeout')), 2000)
      );

      const queryPromise = supabase
        .from('telegram_users')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .single();

      const { data, error } = await Promise.race([queryPromise, timeoutPromise]) as any;

      if (error || !data) {
        return null;
      }

      return data as TelegramUser;
    } catch (error) {
      console.error('Get telegram user by user id error:', error);
      return null;
    }
  }

  /**
   * Create a session for a Telegram user
   */
  async createSession(telegramUserId: string, expiresInDays: number = 30): Promise<string> {
    try {
      const sessionToken = this.generateSessionToken();
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + expiresInDays);

      const { error } = await supabase
        .from('telegram_sessions')
        .insert({
          telegram_user_id: telegramUserId,
          session_token: sessionToken,
          expires_at: expiresAt.toISOString(),
          is_active: true
        });

      if (error) {
        console.error('Error creating session:', error);
        throw new Error('Failed to create session');
      }

      return sessionToken;
    } catch (error) {
      console.error('Create session error:', error);
      throw error;
    }
  }

  /**
   * Validate a session token
   */
  async validateSession(sessionToken: string): Promise<TelegramUser | null> {
    try {
      const { data: session, error: sessionError } = await supabase
        .from('telegram_sessions')
        .select(`
          *,
          telegram_users (*)
        `)
        .eq('session_token', sessionToken)
        .eq('is_active', true)
        .gt('expires_at', new Date().toISOString())
        .single();

      if (sessionError || !session) {
        return null;
      }

      // Update last activity
      await supabase
        .from('telegram_sessions')
        .update({ last_activity: new Date().toISOString() })
        .eq('id', session.id);

      return session.telegram_users as TelegramUser;
    } catch (error) {
      console.error('Validate session error:', error);
      return null;
    }
  }

  /**
   * Invalidate a session
   */
  async invalidateSession(sessionToken: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('telegram_sessions')
        .update({ is_active: false })
        .eq('session_token', sessionToken);

      return !error;
    } catch (error) {
      console.error('Invalidate session error:', error);
      return false;
    }
  }

  /**
   * Log bot interaction for analytics
   */
  async logInteraction(
    telegramId: string,
    command?: string,
    messageText?: string,
    success: boolean = true,
    errorMessage?: string,
    processingTimeMs?: number,
    metadata: Record<string, any> = {}
  ): Promise<void> {
    try {
      await supabase.rpc('log_bot_interaction', {
        p_telegram_id: parseInt(telegramId),
        p_command: command || null,
        p_message_text: messageText || null,
        p_success: success,
        p_error_message: errorMessage || null,
        p_processing_time_ms: processingTimeMs || null,
        p_metadata: metadata
      });
    } catch (error) {
      console.error('Log interaction error:', error);
      // Don't throw error for logging failures
    }
  }

  /**
   * Get user's auth codes (for debugging/admin)
   */
  async getUserAuthCodes(userId: string): Promise<TelegramAuthCode[]> {
    try {
      const { data, error } = await supabase
        .from('telegram_auth_codes')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) {
        console.error('Error getting auth codes:', error);
        return [];
      }

      return data as TelegramAuthCode[];
    } catch (error) {
      console.error('Get user auth codes error:', error);
      return [];
    }
  }

  /**
   * Clean up expired data
   */
  async cleanupExpiredData(): Promise<number> {
    try {
      const { data, error } = await supabase.rpc('cleanup_telegram_expired_data');

      if (error) {
        console.error('Error cleaning up expired data:', error);
        return 0;
      }

      return data as number;
    } catch (error) {
      console.error('Cleanup expired data error:', error);
      return 0;
    }
  }

  /**
   * Update Telegram user preferences
   */
  async updateUserPreferences(
    telegramId: string,
    preferences: Record<string, any>
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('telegram_users')
        .update({ 
          preferences,
          updated_at: new Date().toISOString()
        })
        .eq('telegram_id', telegramId)
        .eq('is_active', true);

      return !error;
    } catch (error) {
      console.error('Update user preferences error:', error);
      return false;
    }
  }

  /**
   * Get bot interaction statistics
   */
  async getBotStats(telegramUserId?: string, days: number = 30): Promise<{
    totalInteractions: number;
    successfulInteractions: number;
    failedInteractions: number;
    averageProcessingTime: number;
    topCommands: Array<{ command: string; count: number }>;
  }> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      let query = supabase
        .from('bot_interactions')
        .select('*')
        .gte('created_at', startDate.toISOString());

      if (telegramUserId) {
        query = query.eq('telegram_user_id', telegramUserId);
      }

      const { data, error } = await query;

      if (error || !data) {
        return {
          totalInteractions: 0,
          successfulInteractions: 0,
          failedInteractions: 0,
          averageProcessingTime: 0,
          topCommands: []
        };
      }

      const totalInteractions = data.length;
      const successfulInteractions = data.filter(i => i.success).length;
      const failedInteractions = totalInteractions - successfulInteractions;
      
      const processingTimes = data
        .filter(i => i.processing_time_ms)
        .map(i => i.processing_time_ms);
      const averageProcessingTime = processingTimes.length > 0
        ? processingTimes.reduce((a, b) => a + b, 0) / processingTimes.length
        : 0;

      const commandCounts = data
        .filter(i => i.command)
        .reduce((acc, i) => {
          acc[i.command] = (acc[i.command] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);

      const topCommands = Object.entries(commandCounts)
        .map(([command, count]) => ({ command, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      return {
        totalInteractions,
        successfulInteractions,
        failedInteractions,
        averageProcessingTime,
        topCommands
      };
    } catch (error) {
      console.error('Get bot stats error:', error);
      return {
        totalInteractions: 0,
        successfulInteractions: 0,
        failedInteractions: 0,
        averageProcessingTime: 0,
        topCommands: []
      };
    }
  }

  /**
   * Generate a secure session token
   */
  private generateSessionToken(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 64; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
}

// Export singleton instance
export const telegramAuthService = new TelegramAuthService();
