import React, { useState, useEffect } from 'react';
import { X, AlertCircle, CheckCircle, Info, AlertTriangle } from 'lucide-react';
import { announcementService, Announcement } from '../../services/AnnouncementService';
import { useAuth } from '../../contexts/AuthContext';

const typeIcons = {
  info: Info,
  success: CheckCircle,
  warning: AlertTriangle,
  error: AlertCircle
};

const typeStyles = {
  info: 'bg-blue-500 border-blue-600',
  success: 'bg-green-500 border-green-600',
  warning: 'bg-yellow-500 border-yellow-600',
  error: 'bg-red-500 border-red-600'
};

interface ToastAnnouncementProps {
  announcement: Announcement;
  onDismiss: (id: string) => void;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

const ToastAnnouncement: React.FC<ToastAnnouncementProps> = ({
  announcement,
  onDismiss,
  position = 'top-right'
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);

  useEffect(() => {
    // Animate in
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Auto-dismiss after delay if specified
    if (announcement.auto_dismiss_after && announcement.auto_dismiss_after > 0) {
      const timer = setTimeout(() => {
        handleDismiss();
      }, announcement.auto_dismiss_after * 1000);
      return () => clearTimeout(timer);
    }
  }, [announcement.auto_dismiss_after]);

  const handleDismiss = () => {
    setIsLeaving(true);
    setTimeout(() => {
      onDismiss(announcement.id);
    }, 300); // Match animation duration
  };

  const IconComponent = typeIcons[announcement.type];
  const colorClass = typeStyles[announcement.type];

  const positionClasses = {
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4'
  };

  return (
    <div
      className={`fixed ${positionClasses[position]} z-50 max-w-sm w-full transition-all duration-300 ease-in-out transform ${
        isVisible && !isLeaving 
          ? 'translate-x-0 opacity-100' 
          : position.includes('right')
          ? 'translate-x-full opacity-0'
          : '-translate-x-full opacity-0'
      }`}
    >
      <div className={`${colorClass} text-white rounded-lg shadow-lg border-l-4 overflow-hidden`}>
        <div className="p-4">
          <div className="flex items-start gap-3">
            <IconComponent className="h-5 w-5 mt-0.5 flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <h4 className="font-semibold text-sm mb-1">
                {announcement.title}
              </h4>
              <p className="text-sm opacity-90 leading-relaxed">
                {announcement.content.length > 100 
                  ? announcement.content.substring(0, 97) + '...'
                  : announcement.content
                }
              </p>
              {announcement.priority && announcement.priority !== 'normal' && (
                <div className="mt-2">
                  <span className="px-2 py-1 rounded text-xs font-medium bg-white/20">
                    {announcement.priority.toUpperCase()}
                  </span>
                </div>
              )}
            </div>
            <button
              onClick={handleDismiss}
              className="flex-shrink-0 p-1 rounded-full hover:bg-white/20 transition-colors"
              title="Dismiss notification"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
        
        {/* Progress bar for auto-dismiss */}
        {announcement.auto_dismiss_after && announcement.auto_dismiss_after > 0 && (
          <div className="h-1 bg-white/20">
            <div 
              className="h-full bg-white/40 transition-all ease-linear"
              style={{
                animation: `shrink ${announcement.auto_dismiss_after}s linear forwards`
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
};

interface AnnouncementToastManagerProps {
  maxToasts?: number;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

export const AnnouncementToastManager: React.FC<AnnouncementToastManagerProps> = ({
  maxToasts = 3,
  position = 'top-right'
}) => {
  const { user } = useAuth();
  const [toastAnnouncements, setToastAnnouncements] = useState<Announcement[]>([]);
  const [dismissedIds, setDismissedIds] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (user) {
      loadToastAnnouncements();
      
      // Check for new announcements every 30 seconds
      const interval = setInterval(loadToastAnnouncements, 30000);
      return () => clearInterval(interval);
    }
  }, [user]);

  useEffect(() => {
    // Load dismissed announcements from localStorage
    const dismissed = localStorage.getItem('dismissed-toast-announcements');
    if (dismissed) {
      try {
        setDismissedIds(new Set(JSON.parse(dismissed)));
      } catch (error) {
        console.error('Error loading dismissed toast announcements:', error);
      }
    }
  }, []);

  const loadToastAnnouncements = async () => {
    try {
      const allAnnouncements = await announcementService.getActiveAnnouncements();
      
      // Filter for high priority announcements suitable for toasts
      const toastWorthy = allAnnouncements.filter(announcement => 
        (announcement.priority === 'high' || announcement.priority === 'urgent') &&
        !dismissedIds.has(announcement.id)
      );
      
      // Only show new announcements (not already displayed)
      const newAnnouncements = toastWorthy.filter(announcement => 
        !toastAnnouncements.some(existing => existing.id === announcement.id)
      );
      
      if (newAnnouncements.length > 0) {
        setToastAnnouncements(prev => 
          [...prev, ...newAnnouncements].slice(-maxToasts)
        );
      }
    } catch (error) {
      console.error('Error loading toast announcements:', error);
    }
  };

  const handleDismiss = (announcementId: string) => {
    // Remove from current toasts
    setToastAnnouncements(prev => prev.filter(a => a.id !== announcementId));
    
    // Add to dismissed list
    const newDismissedIds = new Set(dismissedIds);
    newDismissedIds.add(announcementId);
    setDismissedIds(newDismissedIds);
    
    // Save to localStorage
    localStorage.setItem('dismissed-toast-announcements', JSON.stringify(Array.from(newDismissedIds)));
  };

  return (
    <>
      {toastAnnouncements.map((announcement, index) => (
        <div
          key={announcement.id}
          style={{
            [position.includes('top') ? 'top' : 'bottom']: `${4 + index * 120}px`
          }}
        >
          <ToastAnnouncement
            announcement={announcement}
            onDismiss={handleDismiss}
            position={position}
          />
        </div>
      ))}
      
      <style jsx="true">{`
        @keyframes shrink {
          from { width: 100%; }
          to { width: 0%; }
        }
      `}</style>
    </>
  );
};

// Hook for managing toast announcements
export const useToastAnnouncements = () => {
  const { user } = useAuth();
  const [hasNewToasts, setHasNewToasts] = useState(false);

  useEffect(() => {
    if (user) {
      checkForNewToasts();
    }
  }, [user]);

  const checkForNewToasts = async () => {
    try {
      const allAnnouncements = await announcementService.getActiveAnnouncements();
      const dismissed = JSON.parse(localStorage.getItem('dismissed-toast-announcements') || '[]');
      
      const newToasts = allAnnouncements.filter(announcement => 
        (announcement.priority === 'high' || announcement.priority === 'urgent') &&
        !dismissed.includes(announcement.id)
      );
      
      setHasNewToasts(newToasts.length > 0);
    } catch (error) {
      console.error('Error checking for new toasts:', error);
    }
  };

  return {
    hasNewToasts,
    checkForNewToasts
  };
};
