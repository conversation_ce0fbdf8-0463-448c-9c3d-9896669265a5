/**
 * Command Handler
 * 
 * Handles all bot commands
 */

import { logger } from '../utils/logger.js';

export class CommandHandler {
  constructor(bot) {
    this.bot = bot;
    this.telegramBot = bot.bot;
    this.database = bot.database;
  }

  async handle(msg) {
    const command = msg.text.split(' ')[0].toLowerCase();
    const args = msg.text.split(' ').slice(1);

    logger.logBotInteraction({
      userId: msg.from.id,
      username: msg.from.username,
      command: command,
      messageType: 'command'
    });

    try {
      switch (command) {
        case '/start':
          await this.handleStart(msg);
          break;
        case '/help':
          await this.handleHelp(msg);
          break;
        case '/link':
          await this.handleLink(msg, args);
          break;
        case '/status':
          await this.handleStatus(msg);
          break;
        case '/balance':
          await this.handleBalance(msg);
          break;
        case '/expense':
          await this.handleExpense(msg, args);
          break;
        case '/income':
          await this.handleIncome(msg, args);
          break;
        case '/recent':
          await this.handleRecent(msg);
          break;
        case '/insights':
          await this.handleInsights(msg);
          break;
        case '/categories':
          await this.handleCategories(msg);
          break;
        case '/settings':
          await this.handleSettings(msg);
          break;
        default:
          await this.handleUnknownCommand(msg);
      }
    } catch (error) {
      logger.error('Command handling error:', error);
      await this.telegramBot.sendMessage(msg.chat.id, 
        '❌ Sorry, something went wrong. Please try again later.');
    }
  }

  async handleStart(msg) {
    const welcomeMessage = `
🎉 *Welcome to FiNManageR!*

I'm your personal finance assistant. I can help you:

💰 Track expenses and income
📊 Monitor your budget
📈 Get spending insights
📸 Process receipt photos
🎤 Handle voice commands

*Getting Started:*
1. Link your FiNManageR account: /link <code>
2. Start logging transactions: /expense 50 food Coffee
3. Check your balance: /balance

Type /help for all available commands.

*Need help?* Visit: ${this.bot.config.WEBAPP_URL || 'https://finmanager.netlify.app'}
    `;

    await this.telegramBot.sendMessage(msg.chat.id, welcomeMessage, {
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: [
          [
            { text: '🔗 Link Account', callback_data: 'link_account' },
            { text: '📱 Open Web App', url: this.bot.config.WEBAPP_URL || 'https://finmanager.netlify.app' }
          ],
          [
            { text: '❓ Help', callback_data: 'help' },
            { text: '⚙️ Settings', callback_data: 'settings' }
          ]
        ]
      }
    });
  }

  async handleHelp(msg) {
    const helpMessage = `
📋 *Available Commands:*

*Basic Commands:*
/start - Welcome message and setup
/help - Show this help message
/link <code> - Link your FiNManageR account
/status - Check account status and balance

*Financial Commands:*
/expense <amount> <category> [description] - Log an expense
/income <amount> <source> [description] - Log income
/balance - Check current balance
/recent - View recent transactions
/insights - Get AI spending analysis
/categories - View available categories

*Advanced Features:*
📸 Send receipt photos for automatic processing
🎤 Send voice messages to log transactions
💬 Use natural language: "Spent 500 on lunch"

*Examples:*
\`/expense 50 food Coffee at Starbucks\`
\`/income 5000 salary Monthly salary\`
\`"I spent 200 on groceries"\`

*Settings:*
/settings - Configure bot preferences

Need more help? Visit: ${this.bot.config.WEBAPP_URL || 'https://finmanager.netlify.app'}
    `;

    await this.telegramBot.sendMessage(msg.chat.id, helpMessage, {
      parse_mode: 'Markdown'
    });
  }

  async handleLink(msg, args) {
    if (args.length === 0) {
      await this.telegramBot.sendMessage(msg.chat.id,
        '🔗 To link your account, use: /link <your-8-digit-code>\n\n' +
        '📱 Get your permanent 8-digit authentication code from the FiNManageR web app:\n' +
        `${this.bot.config.WEBAPP_URL || 'https://finmanager.netlify.app'}/settings/telegram\n\n` +
        '✨ Your code never expires and can be used multiple times!');
      return;
    }

    const code = args[0];
    
    try {
      // Validate the linking code
      const linkData = await this.database.validateLinkCode(code);
      
      if (!linkData) {
        await this.telegramBot.sendMessage(msg.chat.id,
          '❌ Invalid authentication code. Please check your 8-digit code from the web app.\n\n' +
          '💡 Make sure you\'re using the permanent code from: Settings → Telegram Integration');
        return;
      }

      // Link the account
      await this.database.linkTelegramAccount(linkData.user_id, msg.from.id, {
        username: msg.from.username,
        first_name: msg.from.first_name,
        last_name: msg.from.last_name
      });

      await this.telegramBot.sendMessage(msg.chat.id, 
        '✅ Account linked successfully!\n\n' +
        'You can now use all bot features. Try:\n' +
        '• /balance - Check your balance\n' +
        '• /expense 50 food Lunch - Log an expense\n' +
        '• /recent - View recent transactions');

    } catch (error) {
      logger.error('Account linking error:', error);
      await this.telegramBot.sendMessage(msg.chat.id, 
        '❌ Failed to link account. Please try again or contact support.');
    }
  }

  async handleStatus(msg) {
    try {
      const user = await this.database.getUserByTelegramId(msg.from.id);
      
      if (!user) {
        await this.telegramBot.sendMessage(msg.chat.id, 
          '❌ Account not linked. Use /link <code> to link your account first.');
        return;
      }

      const statusMessage = `
📊 *Account Status*

👤 *User:* ${user.full_name || 'Not set'}
💰 *Current Balance:* ₹${user.current_balance || 0}
📅 *Member Since:* ${new Date(user.created_at).toLocaleDateString()}
🔗 *Account:* Linked ✅

*Quick Actions:*
• /balance - Detailed balance
• /recent - Recent transactions
• /expense - Log new expense
      `;

      await this.telegramBot.sendMessage(msg.chat.id, statusMessage, {
        parse_mode: 'Markdown',
        reply_markup: {
          inline_keyboard: [
            [
              { text: '💰 Balance', callback_data: 'balance' },
              { text: '📊 Recent', callback_data: 'recent' }
            ],
            [
              { text: '📱 Open App', url: this.bot.config.WEBAPP_URL || 'https://finmanager.netlify.app' }
            ]
          ]
        }
      });

    } catch (error) {
      logger.error('Status check error:', error);
      await this.telegramBot.sendMessage(msg.chat.id, 
        '❌ Unable to fetch account status. Please try again.');
    }
  }

  async handleBalance(msg) {
    await this.telegramBot.sendMessage(msg.chat.id, 
      '💰 Balance feature coming soon! Use the web app for detailed balance information.');
  }

  async handleExpense(msg, args) {
    if (args.length < 2) {
      await this.telegramBot.sendMessage(msg.chat.id, 
        '💸 Usage: /expense <amount> <category> [description]\n\n' +
        'Examples:\n' +
        '• /expense 50 food Coffee\n' +
        '• /expense 200 transport Uber ride\n' +
        '• /expense 1500 shopping Groceries at mall');
      return;
    }

    await this.telegramBot.sendMessage(msg.chat.id, 
      '💸 Expense logging feature coming soon! Use the web app to log expenses for now.');
  }

  async handleIncome(msg, args) {
    await this.telegramBot.sendMessage(msg.chat.id, 
      '💰 Income logging feature coming soon! Use the web app to log income for now.');
  }

  async handleRecent(msg) {
    await this.telegramBot.sendMessage(msg.chat.id, 
      '📊 Recent transactions feature coming soon! Use the web app to view transactions for now.');
  }

  async handleInsights(msg) {
    await this.telegramBot.sendMessage(msg.chat.id, 
      '🤖 AI insights feature coming soon! Use the web app for detailed insights for now.');
  }

  async handleCategories(msg) {
    const categoriesMessage = `
📂 *Available Categories:*

💰 *Income:*
• salary - Monthly salary
• freelance - Freelance work
• investment - Investment returns
• other - Other income

💸 *Expenses:*
• food - Food & dining
• transport - Transportation
• shopping - Shopping & retail
• bills - Utilities & bills
• entertainment - Entertainment
• health - Healthcare
• education - Education
• other - Other expenses

*Usage:* /expense 50 food Coffee
    `;

    await this.telegramBot.sendMessage(msg.chat.id, categoriesMessage, {
      parse_mode: 'Markdown'
    });
  }

  async handleSettings(msg) {
    await this.telegramBot.sendMessage(msg.chat.id, 
      '⚙️ Settings feature coming soon! Use the web app for settings configuration.');
  }

  async handleUnknownCommand(msg) {
    await this.telegramBot.sendMessage(msg.chat.id, 
      '❓ Unknown command. Type /help to see all available commands.');
  }
}
