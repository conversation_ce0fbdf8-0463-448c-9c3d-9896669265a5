/**
 * Notification Triggers Hook
 * 
 * Phase 2 enhancement: Automatic notification triggers
 * Integrates with app events to create relevant notifications
 */

import { useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { NotificationTriggers } from '../utils/notificationTriggers';
import { NotificationService } from '../services/NotificationService';
import type { CreateNotificationInput } from '../types/notification';

/**
 * Hook for triggering notifications based on app events
 */
export function useNotificationTriggers() {
  const { user } = useAuth();

  /**
   * Trigger notification when a transaction is added
   */
  const triggerTransactionAdded = useCallback(async (transaction: {
    id: string;
    amount: number;
    type: 'income' | 'expense';
    category: string;
    description?: string;
  }) => {
    if (!user?.id) return;

    try {
      await NotificationTriggers.onTransactionAdded(user.id, transaction);
    } catch (error) {
      console.error('Failed to trigger transaction notification:', error);
    }
  }, [user?.id]);

  /**
   * Trigger notification when budget is exceeded
   */
  const triggerBudgetExceeded = useCallback(async (budget: {
    id: string;
    name: string;
    limit: number;
    spent: number;
  }) => {
    if (!user?.id) return;

    try {
      await NotificationTriggers.onBudgetExceeded(user.id, budget);
    } catch (error) {
      console.error('Failed to trigger budget exceeded notification:', error);
    }
  }, [user?.id]);

  /**
   * Trigger notification when approaching budget limit
   */
  const triggerBudgetWarning = useCallback(async (budget: {
    id: string;
    name: string;
    limit: number;
    spent: number;
    threshold: number;
  }) => {
    if (!user?.id) return;

    try {
      await NotificationTriggers.onBudgetWarning(user.id, budget);
    } catch (error) {
      console.error('Failed to trigger budget warning notification:', error);
    }
  }, [user?.id]);

  /**
   * Trigger notification for payment due
   */
  const triggerPaymentDue = useCallback(async (payment: {
    id: string;
    name: string;
    amount: number;
    due_date: string;
  }) => {
    if (!user?.id) return;

    try {
      await NotificationTriggers.onPaymentDue(user.id, payment);
    } catch (error) {
      console.error('Failed to trigger payment due notification:', error);
    }
  }, [user?.id]);

  /**
   * Trigger system notification
   */
  const triggerSystemMessage = useCallback(async (
    message: string,
    priority: 'low' | 'medium' | 'high' = 'medium'
  ) => {
    if (!user?.id) return;

    try {
      await NotificationTriggers.onSystemUpdate(user.id, message, priority);
    } catch (error) {
      console.error('Failed to trigger system notification:', error);
    }
  }, [user?.id]);

  /**
   * Trigger data sync notification
   */
  const triggerDataSync = useCallback(async (
    status: 'success' | 'error',
    message: string
  ) => {
    if (!user?.id) return;

    try {
      await NotificationTriggers.onDataSync(user.id, status, message);
    } catch (error) {
      console.error('Failed to trigger data sync notification:', error);
    }
  }, [user?.id]);

  /**
   * Trigger notification when budget is created
   */
  const triggerBudgetCreated = useCallback(async (budget: {
    category: string;
    amount: number;
  }) => {
    if (!user?.id) return;

    try {
      const notification: CreateNotificationInput = {
        user_id: user.id,
        type: 'budget',
        priority: 'low',
        title: 'Budget Created',
        message: `New budget created for ${budget.category} with limit of $${budget.amount.toFixed(2)}`,
        action_url: '/budget',
        action_text: 'View Budgets',
        metadata: {
          budget_category: budget.category,
          budget_amount: budget.amount,
          action: 'created'
        }
      };

      await NotificationService.create(notification);
    } catch (error) {
      console.error('Failed to trigger budget created notification:', error);
    }
  }, [user?.id]);

  /**
   * Trigger notification when budget is updated
   */
  const triggerBudgetUpdated = useCallback(async (budget: {
    category: string;
    amount: number;
  }) => {
    if (!user?.id) return;

    try {
      const notification: CreateNotificationInput = {
        user_id: user.id,
        type: 'budget',
        priority: 'low',
        title: 'Budget Updated',
        message: `Budget for ${budget.category} updated to $${budget.amount.toFixed(2)}`,
        action_url: '/budget',
        action_text: 'View Budgets',
        metadata: {
          budget_category: budget.category,
          budget_amount: budget.amount,
          action: 'updated'
        }
      };

      await NotificationService.create(notification);
    } catch (error) {
      console.error('Failed to trigger budget updated notification:', error);
    }
  }, [user?.id]);

  /**
   * Trigger notification when payment is made
   */
  const triggerPaymentMade = useCallback(async (payment: {
    amount: number;
    type: string;
    description?: string;
  }) => {
    if (!user?.id) return;

    try {
      const notification: CreateNotificationInput = {
        user_id: user.id,
        type: 'payment',
        priority: 'low',
        title: 'Payment Made',
        message: `${payment.type} payment of $${payment.amount.toFixed(2)} has been processed${payment.description ? ` - ${payment.description}` : ''}`,
        action_url: '/transactions',
        action_text: 'View Transactions',
        metadata: {
          payment_amount: payment.amount,
          payment_type: payment.type,
          payment_description: payment.description,
          action: 'payment_made'
        }
      };

      await NotificationService.create(notification);
    } catch (error) {
      console.error('Failed to trigger payment made notification:', error);
    }
  }, [user?.id]);

  /**
   * Check and trigger budget notifications based on current spending
   */
  const checkBudgetStatus = useCallback(async (budgets: Array<{
    id: string;
    name: string;
    limit: number;
    spent: number;
  }>) => {
    if (!user?.id) return;

    for (const budget of budgets) {
      const percentage = budget.spent / budget.limit;

      // Trigger warning at 80% of budget
      if (percentage >= 0.8 && percentage < 1.0) {
        await triggerBudgetWarning({
          ...budget,
          threshold: 0.8
        });
      }

      // Trigger exceeded notification at 100%
      if (percentage >= 1.0) {
        await triggerBudgetExceeded(budget);
      }
    }
  }, [user?.id, triggerBudgetWarning, triggerBudgetExceeded]);

  /**
   * Auto-trigger notifications for large transactions
   */
  const checkTransactionThresholds = useCallback(async (transaction: {
    id: string;
    amount: number;
    type: 'income' | 'expense';
    category: string;
    description?: string;
  }) => {
    if (!user?.id) return;

    const amount = Math.abs(transaction.amount);
    
    // Trigger notification for transactions over $500
    if (amount >= 500) {
      await triggerTransactionAdded(transaction);
    }
  }, [user?.id, triggerTransactionAdded]);

  return {
    triggerTransactionAdded,
    triggerBudgetCreated,
    triggerBudgetUpdated,
    triggerBudgetExceeded,
    triggerBudgetWarning,
    triggerPaymentDue,
    triggerPaymentMade,
    triggerSystemMessage,
    triggerDataSync,
    checkBudgetStatus,
    checkTransactionThresholds,
  };
}
