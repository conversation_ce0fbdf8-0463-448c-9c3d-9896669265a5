/**
 * Simple Notification Service
 * 
 * Clean, focused service for managing notifications
 * Avoids complexity that caused issues in previous implementation
 */

import { supabase } from '../lib/supabase';
import { 
  Notification, 
  NotificationPreferences, 
  CreateNotificationInput, 
  UpdateNotificationPreferencesInput,
  NotificationQueryOptions,
  DEFAULT_NOTIFICATION_PREFERENCES
} from '../types/notification';

/**
 * Notification Service Class
 * Handles all notification-related database operations
 */
export class NotificationService {
  /**
   * Create a new notification
   */
  static async create(input: CreateNotificationInput): Promise<Notification> {
    const { data, error } = await supabase
      .from('notifications')
      .insert({
        user_id: input.user_id,
        type: input.type,
        priority: input.priority || 'medium',
        title: input.title,
        message: input.message,
        action_url: input.action_url,
        action_text: input.action_text,
        expires_at: input.expires_at,
        metadata: input.metadata || {}
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create notification: ${error.message}`);
    }

    return data;
  }

  /**
   * Get notifications for a user
   */
  static async getUserNotifications(
    userId: string, 
    options: NotificationQueryOptions = {}
  ): Promise<Notification[]> {
    let query = supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId);

    // Apply filters
    if (options.type) {
      query = query.eq('type', options.type);
    }

    if (options.priority) {
      query = query.eq('priority', options.priority);
    }

    if (options.is_read !== undefined) {
      query = query.eq('is_read', options.is_read);
    }

    // Handle expired notifications
    if (!options.include_expired) {
      query = query.or('expires_at.is.null,expires_at.gt.now()');
    }

    // Apply ordering and pagination
    query = query.order('created_at', { ascending: false });

    if (options.limit) {
      query = query.limit(options.limit);
    }

    if (options.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 50) - 1);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch notifications: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get unread notification count for a user
   */
  static async getUnreadCount(userId: string): Promise<number> {
    try {
      // Try RPC function first, fallback to direct query
      const { data, error } = await supabase
        .rpc('get_unread_notification_count', { p_user_id: userId });

      if (error) {
        console.warn('RPC function not available, using fallback query:', error.message);

        // Fallback: Direct query to notifications table
        const { count, error: countError } = await supabase
          .from('notifications')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', userId)
          .or('is_read.is.null,is_read.eq.false')
          .or('expires_at.is.null,expires_at.gt.now()');

        if (countError) {
          console.warn('Notifications table query failed, returning 0:', countError.message);
          return 0;
        }

        return count || 0;
      }

      return data || 0;
    } catch (error) {
      console.error('Error getting unread notification count:', error);
      return 0;
    }
  }

  /**
   * Mark notification as read
   */
  static async markAsRead(notificationId: string): Promise<void> {
    const { error } = await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('id', notificationId);

    if (error) {
      throw new Error(`Failed to mark notification as read: ${error.message}`);
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  static async markAllAsRead(userId: string): Promise<void> {
    const { error } = await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('user_id', userId)
      .eq('is_read', false);

    if (error) {
      throw new Error(`Failed to mark all notifications as read: ${error.message}`);
    }
  }

  /**
   * Delete a notification
   */
  static async delete(notificationId: string): Promise<void> {
    const { error } = await supabase
      .from('notifications')
      .delete()
      .eq('id', notificationId);

    if (error) {
      throw new Error(`Failed to delete notification: ${error.message}`);
    }
  }

  /**
   * Clean up expired notifications
   */
  static async cleanupExpired(): Promise<number> {
    const { data, error } = await supabase
      .rpc('cleanup_expired_notifications');

    if (error) {
      throw new Error(`Failed to cleanup expired notifications: ${error.message}`);
    }

    return data || 0;
  }

  /**
   * Get user notification preferences
   */
  static async getPreferences(userId: string): Promise<NotificationPreferences> {
    const { data, error } = await supabase
      .from('notification_preferences')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No preferences found, create default ones
        return await this.createDefaultPreferences(userId);
      }
      throw new Error(`Failed to get notification preferences: ${error.message}`);
    }

    return data;
  }

  /**
   * Update user notification preferences
   */
  static async updatePreferences(
    userId: string, 
    updates: UpdateNotificationPreferencesInput
  ): Promise<NotificationPreferences> {
    const { data, error } = await supabase
      .from('notification_preferences')
      .update(updates)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update notification preferences: ${error.message}`);
    }

    return data;
  }

  /**
   * Create default notification preferences for a user
   */
  static async createDefaultPreferences(userId: string): Promise<NotificationPreferences> {
    const { data, error } = await supabase
      .from('notification_preferences')
      .insert({
        user_id: userId,
        ...DEFAULT_NOTIFICATION_PREFERENCES
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create default preferences: ${error.message}`);
    }

    return data;
  }

  /**
   * Check if user should receive notification based on preferences
   */
  static async shouldNotify(
    userId: string, 
    type: string, 
    priority: string = 'medium'
  ): Promise<boolean> {
    try {
      const preferences = await this.getPreferences(userId);
      
      // Check if notification type is enabled
      switch (type) {
        case 'transaction':
          if (!preferences.transaction_alerts) return false;
          break;
        case 'budget':
          if (!preferences.budget_warnings) return false;
          break;
        case 'payment':
          if (!preferences.payment_reminders) return false;
          break;
        case 'system':
          if (!preferences.system_messages) return false;
          break;
        default:
          return false;
      }

      // Check quiet hours
      if (preferences.quiet_hours_start && preferences.quiet_hours_end) {
        const now = new Date();
        const currentTime = now.toTimeString().slice(0, 5); // HH:MM format
        
        if (currentTime >= preferences.quiet_hours_start && 
            currentTime <= preferences.quiet_hours_end) {
          // Only allow high priority notifications during quiet hours
          return priority === 'high';
        }
      }

      return true;
    } catch (error) {
      // If we can't check preferences, default to allowing the notification
      console.error('Error checking notification preferences:', error);
      return true;
    }
  }
}
