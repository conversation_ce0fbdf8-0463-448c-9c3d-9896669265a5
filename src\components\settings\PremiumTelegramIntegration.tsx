import React, { useState, useEffect } from 'react';
import { 
  MessageCircle, 
  Copy, 
  Check, 
  ExternalLink, 
  Smartphone, 
  Shield, 
  Zap, 
  Crown, 
  Lock, 
  Star,
  Loader2,
  AlertCircle,
  CheckCircle,
  Sparkles
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { telegramAuthService } from '../../services/TelegramAuthService';
import { premiumFeatureService, UserSubscription } from '../../services/PremiumFeatureService';
// import { usePerformanceMonitor, useRenderPerformance } from '../../utils/performanceMonitor';

interface PremiumTelegramIntegrationProps {
  className?: string;
}

export const PremiumTelegramIntegration: React.FC<PremiumTelegramIntegrationProps> = ({ className }) => {
  const { user } = useAuth();
  // const { time } = usePerformanceMonitor();

  // Monitor render performance
  // useRenderPerformance('PremiumTelegramIntegration');

  const [permanentAuthCode, setPermanentAuthCode] = useState<{code: string, created_at: string, usage_count: number} | null>(null);
  const [loading, setLoading] = useState(false);
  const [copied, setCopied] = useState(false);
  const [telegramUser, setTelegramUser] = useState<any>(null);
  const [loadingStatus, setLoadingStatus] = useState(true);
  const [hasAccess, setHasAccess] = useState(false);
  const [accessReason, setAccessReason] = useState<string>('');
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [featureEnabled, setFeatureEnabled] = useState(true);
  const [regeneratingCode, setRegeneratingCode] = useState(false);

  useEffect(() => {
    let isMounted = true;

    const initializeComponent = async () => {
      if (!user?.id || !isMounted) return;

      try {
        // Shorter timeout and run operations in parallel with individual timeouts
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Component initialization timeout')), 3000)
        );

        // Run all operations with their own timeouts
        await Promise.race([
          Promise.allSettled([
            checkFeatureAccess(),
            loadTelegramStatus(),
            loadPermanentAuthCode()
          ]),
          timeoutPromise
        ]);
      } catch (error) {
        console.error('Component initialization error:', error);
        if (isMounted) {
          // Set safe defaults on error - always allow access to prevent blocking users
          setHasAccess(true);
          setAccessReason('Access granted (initialization error)');
          setFeatureEnabled(true);
          setLoadingStatus(false);
          setTelegramUser(null); // Assume not linked on error
        }
      }
    };

    initializeComponent();

    return () => {
      isMounted = false;
    };
  }, [user?.id]); // Only depend on user.id, not the entire user object

  const checkFeatureAccess = async () => {
    if (!user?.id) return;

    try {
      // Shorter timeout and better error handling
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Feature access check timeout')), 2000)
      );

      const accessPromise = premiumFeatureService.hasFeatureAccess(user.id, 'telegram_integration');

      const accessResult = await Promise.race([accessPromise, timeoutPromise]) as any;

      setHasAccess(accessResult?.hasAccess ?? true); // Default to true
      setAccessReason(accessResult?.reason || 'Access granted');
      setSubscription(accessResult?.subscription || null);
      setFeatureEnabled(accessResult?.featureFlag?.is_enabled !== false); // Default to true
    } catch (error) {
      console.error('Error checking feature access:', error);
      // Always grant access on error to prevent blocking users
      setHasAccess(true);
      setAccessReason('Access granted (service unavailable)');
      setFeatureEnabled(true);
    }
  };

  const loadTelegramStatus = async () => {
    if (!user?.id) return;

    try {
      setLoadingStatus(true);

      // Shorter timeout and better error handling
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Telegram status load timeout')), 2000)
      );

      const statusPromise = telegramAuthService.getTelegramUserByUserId(user.id);

      const linkedUser = await Promise.race([statusPromise, timeoutPromise]) as any;
      setTelegramUser(linkedUser || null);
    } catch (error) {
      console.error('Failed to load Telegram status:', error);
      // Set null user on error (not linked) - this is expected behavior
      setTelegramUser(null);
    } finally {
      setLoadingStatus(false);
    }
  };

  const loadPermanentAuthCode = async () => {
    if (!user?.id || !hasAccess) return;

    try {
      setLoading(true);
      const authCodeData = await telegramAuthService.getPermanentAuthCode(user.id);
      setPermanentAuthCode(authCodeData);

      // Record feature usage
      await premiumFeatureService.recordFeatureUsage(user.id, 'telegram_integration');
    } catch (error) {
      console.error('Failed to load permanent auth code:', error);
      // Don't show alert for loading errors, just log them
    } finally {
      setLoading(false);
    }
  };

  const regenerateAuthCode = async () => {
    if (!user?.id || !hasAccess) return;

    if (!confirm('Are you sure you want to regenerate your authentication code? Your old code will no longer work.')) {
      return;
    }

    try {
      setRegeneratingCode(true);
      const newCode = await telegramAuthService.regeneratePermanentAuthCode(user.id);
      setPermanentAuthCode({
        code: newCode,
        created_at: new Date().toISOString(),
        usage_count: 0
      });
      alert('New authentication code generated successfully!');
    } catch (error) {
      console.error('Failed to regenerate auth code:', error);
      alert('Failed to regenerate authentication code. Please try again.');
    } finally {
      setRegeneratingCode(false);
    }
  };

  const copyToClipboard = async () => {
    try {
      if (permanentAuthCode?.code) {
        await navigator.clipboard.writeText(permanentAuthCode.code);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      }
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const unlinkAccount = async () => {
    if (!telegramUser || !confirm('Are you sure you want to unlink your Telegram account?')) {
      return;
    }

    try {
      setLoading(true);
      const success = await telegramAuthService.unlinkTelegramAccount(telegramUser.telegram_id);

      if (success) {
        setTelegramUser(null);
        setAuthCode('');
        alert('Telegram account unlinked successfully!');
      } else {
        alert('Failed to unlink account. Please try again.');
      }
    } catch (error) {
      console.error('Failed to unlink account:', error);
      alert('Failed to unlink account. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const quickFixLink = () => {
    if (!user?.id) return;

    // Set manual link using localStorage
    telegramAuthService.setManualLink(user.id);

    // Reload the status
    loadTelegramStatus();

    alert('Account link restored! This is a temporary fix using localStorage.');
  };

  const handleUpgrade = () => {
    // Navigate to upgrade page or open upgrade modal
    window.location.href = '/upgrade';
  };

  if (loadingStatus) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        </div>
      </div>
    );
  }

  // Feature disabled by admin
  if (!featureEnabled) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="flex items-center gap-3 mb-4">
          <MessageCircle className="h-6 w-6 text-gray-400" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Telegram Integration
          </h3>
          <div className="flex items-center gap-2">
            <Crown className="h-4 w-4 text-yellow-500" />
            <span className="text-xs font-medium text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 px-2 py-1 rounded-full">
              PREMIUM
            </span>
          </div>
        </div>
        
        <div className="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <AlertCircle className="h-5 w-5 text-gray-500" />
            <span className="font-medium text-gray-700 dark:text-gray-300">
              Feature Temporarily Unavailable
            </span>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            The Telegram Integration feature is currently disabled by the administrator. 
            Please check back later or contact support for more information.
          </p>
        </div>
      </div>
    );
  }

  // No access (premium required)
  if (!hasAccess) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="flex items-center gap-3 mb-4">
          <MessageCircle className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Telegram Integration
          </h3>
          <div className="flex items-center gap-2">
            <Crown className="h-4 w-4 text-yellow-500" />
            <span className="text-xs font-medium text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 px-2 py-1 rounded-full">
              PREMIUM
            </span>
          </div>
        </div>

        {/* Premium Feature Showcase */}
        <div className="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6 mb-6">
          <div className="flex items-center gap-2 mb-4">
            <Sparkles className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            <span className="font-semibold text-blue-800 dark:text-blue-300">
              AI-Powered Financial Assistant
            </span>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div className="flex items-start gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <MessageCircle className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                  Natural Language Processing
                </h4>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  "I spent 500 on lunch" → Automatically categorized expense
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                <Smartphone className="h-4 w-4 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                  Voice & Photo Recognition
                </h4>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  Send voice messages or receipt photos for instant logging
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                <Zap className="h-4 w-4 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                  Interactive Confirmations
                </h4>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  One-click approve, edit, or discard with inline buttons
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="p-2 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
                <Shield className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                  Family Collaboration
                </h4>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  Share expenses with family and manage group budgets
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
            <div className="flex items-center gap-2 mb-2">
              <Lock className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {accessReason}
              </span>
            </div>
            <p className="text-xs text-gray-600 dark:text-gray-400 mb-4">
              Upgrade to Premium to unlock the full power of AI-driven financial management through Telegram.
            </p>
            
            <div className="flex gap-3">
              <button
                onClick={handleUpgrade}
                className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 px-4 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center justify-center gap-2 text-sm font-medium"
              >
                <Crown className="h-4 w-4" />
                Upgrade to Premium
              </button>
              <button
                onClick={() => window.open('/features', '_blank')}
                className="px-4 py-2 text-blue-600 dark:text-blue-400 border border-blue-200 dark:border-blue-700 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors text-sm"
              >
                Learn More
              </button>
            </div>
          </div>
        </div>

        {/* Current Plan Info */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Current Plan: {subscription?.plan_type?.toUpperCase() || 'FREE'}
              </span>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {subscription?.features?.length || 0} features included
              </p>
            </div>
            <Star className="h-5 w-5 text-yellow-500" />
          </div>
        </div>
      </div>
    );
  }

  // User has access - show full integration component
  return (
    <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
      <div className="flex items-center gap-3 mb-6">
        <MessageCircle className="h-6 w-6 text-blue-600 dark:text-blue-400" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Telegram Integration
        </h3>
        <div className="flex items-center gap-2">
          <Crown className="h-4 w-4 text-yellow-500" />
          <span className="text-xs font-medium text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 px-2 py-1 rounded-full">
            PREMIUM
          </span>
          <CheckCircle className="h-4 w-4 text-green-500" />
          <span className="text-xs font-medium text-green-600 bg-green-100 dark:bg-green-900/30 px-2 py-1 rounded-full">
            ACTIVE
          </span>
        </div>
      </div>

      {/* Status Section */}
      <div className="mb-6">
        {telegramUser ? (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
              <span className="font-medium text-green-800 dark:text-green-400">
                Telegram Account Linked
              </span>
            </div>
            <div className="text-sm text-green-700 dark:text-green-300 space-y-1">
              <p><strong>Username:</strong> {telegramUser.username || 'Not set'}</p>
              <p><strong>Name:</strong> {telegramUser.first_name} {telegramUser.last_name}</p>
              <p><strong>Linked:</strong> {new Date(telegramUser.linked_at).toLocaleDateString()}</p>
            </div>
            <div className="mt-3 flex gap-3">
              <button
                onClick={unlinkAccount}
                disabled={loading}
                className="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 disabled:opacity-50"
              >
                Unlink Account
              </button>
            </div>
          </div>
        ) : (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <MessageCircle className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              <span className="font-medium text-blue-800 dark:text-blue-400">
                Ready to Link Telegram
              </span>
            </div>
            <p className="text-sm text-blue-700 dark:text-blue-300 mb-3">
              Connect your Telegram account to access our AI-powered financial assistant!
            </p>
            <button
              onClick={quickFixLink}
              className="text-xs bg-green-600 text-white px-3 py-1 rounded hover:bg-green-700 transition-colors"
            >
              Quick Fix: Restore Link (Temporary)
            </button>
          </div>
        )}
      </div>

      {/* Premium Features Section */}
      <div className="mb-6">
        <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center gap-2">
          <Sparkles className="h-4 w-4 text-purple-500" />
          Premium AI Features:
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-start gap-3 p-3 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg border border-blue-200 dark:border-blue-700">
            <MessageCircle className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5" />
            <div>
              <h5 className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                Natural Language AI
              </h5>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                "Spent 500 on lunch" → Automatically categorized & confirmed
              </p>
            </div>
          </div>
          <div className="flex items-start gap-3 p-3 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg border border-green-200 dark:border-green-700">
            <Smartphone className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5" />
            <div>
              <h5 className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                Voice & Photo Processing
              </h5>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                Send voice messages or receipt photos for instant logging
              </p>
            </div>
          </div>
          <div className="flex items-start gap-3 p-3 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg border border-purple-200 dark:border-purple-700">
            <Zap className="h-5 w-5 text-purple-600 dark:text-purple-400 mt-0.5" />
            <div>
              <h5 className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                Interactive Confirmations
              </h5>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                One-click approve, edit, or discard with inline buttons
              </p>
            </div>
          </div>
          <div className="flex items-start gap-3 p-3 bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 rounded-lg border border-yellow-200 dark:border-yellow-700">
            <Shield className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5" />
            <div>
              <h5 className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                Family Collaboration
              </h5>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                Share expenses and manage family budgets together
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Setup or Commands Section */}
      {!telegramUser ? (
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900 dark:text-gray-100">
            Setup Instructions:
          </h4>
          
          <div className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
            <div className="flex gap-3">
              <span className="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-xs font-medium">
                1
              </span>
              <div>
                <p className="font-medium text-gray-900 dark:text-gray-100">
                  Find our Telegram Bot
                </p>
                <p>Search for <code className="bg-gray-100 dark:bg-gray-700 px-1 rounded">@FiNManageRBot</code> on Telegram</p>
                <a
                  href="https://t.me/FiNManageRBot"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-1 text-blue-600 dark:text-blue-400 hover:underline mt-1"
                >
                  Open in Telegram <ExternalLink className="h-3 w-3" />
                </a>
              </div>
            </div>

            <div className="flex gap-3">
              <span className="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-xs font-medium">
                2
              </span>
              <div>
                <p className="font-medium text-gray-900 dark:text-gray-100">
                  Your Permanent Auth Code
                </p>
                <p>Use this permanent code to link your Telegram account</p>
              </div>
            </div>

            <div className="flex gap-3">
              <span className="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-xs font-medium">
                3
              </span>
              <div>
                <p className="font-medium text-gray-900 dark:text-gray-100">
                  Link Your Account
                </p>
                <p>Send the auth code to the bot using: <code className="bg-gray-100 dark:bg-gray-700 px-1 rounded">/link YOUR_CODE</code></p>
              </div>
            </div>
          </div>

          {/* Permanent Auth Code Display */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
            {!permanentAuthCode ? (
              <div className="text-center py-4">
                {loading ? (
                  <div className="flex items-center justify-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Loading your auth code...</span>
                  </div>
                ) : (
                  <div className="text-gray-500 dark:text-gray-400">
                    <p>Unable to load authentication code.</p>
                    <button
                      onClick={loadPermanentAuthCode}
                      className="text-blue-600 dark:text-blue-400 hover:underline mt-2"
                    >
                      Try Again
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-green-800 dark:text-green-400">
                      Your Permanent Authentication Code:
                    </span>
                    <button
                      onClick={copyToClipboard}
                      className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300"
                      title="Copy to clipboard"
                    >
                      {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </button>
                  </div>
                  <div className="font-mono text-3xl font-bold text-green-900 dark:text-green-300 tracking-wider text-center py-2">
                    {permanentAuthCode.code}
                  </div>
                  <div className="text-xs text-green-700 dark:text-green-400 mt-3 space-y-1">
                    <p><strong>Command:</strong> <code>/link {permanentAuthCode.code}</code></p>
                    <p><strong>Created:</strong> {new Date(permanentAuthCode.created_at).toLocaleDateString()}</p>
                    <p><strong>Times Used:</strong> {permanentAuthCode.usage_count}</p>
                  </div>
                </div>

                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                  <h5 className="font-medium text-blue-800 dark:text-blue-400 mb-2">How to Link:</h5>
                  <ol className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                    <li>1. Open Telegram and find @Myfnmbot</li>
                    <li>2. Send: <code>/link {permanentAuthCode.code}</code></li>
                    <li>3. Your account will be linked permanently!</li>
                  </ol>
                </div>

                <div className="flex gap-3">
                  <button
                    onClick={regenerateAuthCode}
                    disabled={regeneratingCode}
                    className="text-sm text-orange-600 dark:text-orange-400 hover:underline disabled:opacity-50 flex items-center gap-1"
                  >
                    {regeneratingCode ? (
                      <>
                        <Loader2 className="h-3 w-3 animate-spin" />
                        Regenerating...
                      </>
                    ) : (
                      'Regenerate Code'
                    )}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
          <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center gap-2">
            <Sparkles className="h-4 w-4 text-purple-500" />
            Premium Bot Commands:
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
            <div className="space-y-2">
              <div>
                <code className="bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 px-2 py-1 rounded text-xs">
                  "I spent 500 on lunch"
                </code>
                <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">Natural language expense</p>
              </div>
              <div>
                <code className="bg-gradient-to-r from-green-100 to-blue-100 dark:from-green-900/30 dark:to-blue-900/30 px-2 py-1 rounded text-xs">
                  Send voice message
                </code>
                <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">Voice transaction logging</p>
              </div>
            </div>
            <div className="space-y-2">
              <div>
                <code className="bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900/30 dark:to-pink-900/30 px-2 py-1 rounded text-xs">
                  Send receipt photo
                </code>
                <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">OCR receipt scanning</p>
              </div>
              <div>
                <code className="bg-gradient-to-r from-yellow-100 to-orange-100 dark:from-yellow-900/30 dark:to-orange-900/30 px-2 py-1 rounded text-xs">
                  /family_balance
                </code>
                <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">Family group finances</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PremiumTelegramIntegration;
