/**
 * Premium Feature Management Service
 * Handles feature gating, subscription checks, and admin controls
 */

import { supabase } from '../lib/supabase';

export interface FeatureFlag {
  id: string;
  feature_key: string;
  feature_name: string;
  is_enabled: boolean;
  requires_subscription: boolean;
  description: string;
  category: string;
  is_beta: boolean;
  created_at: string;
  updated_at: string;
  usage_count: number;
  active_users: number;
}

export interface UserSubscription {
  id: string;
  user_id: string;
  plan_type: 'free' | 'premium' | 'enterprise';
  status: 'active' | 'inactive' | 'cancelled' | 'expired';
  features: string[];
  expires_at: string | null;
  created_at: string;
  updated_at: string;
}

export interface FeatureUsage {
  id: string;
  user_id: string;
  feature_name: string;
  usage_count: number;
  last_used: string;
  created_at: string;
}

class PremiumFeatureService {
  /**
   * Check if a user has access to a premium feature
   * TEMPORARY: ALL FEATURES ACCESSIBLE FOR DEBUGGING
   */
  async hasFeatureAccess(userId: string, featureName: string): Promise<{
    hasAccess: boolean;
    reason?: string;
    subscription?: UserSubscription;
    featureFlag?: FeatureFlag;
  }> {
    try {
      // TEMPORARY FIX: Grant access to all features for debugging
      console.log(`🔧 TEMP ACCESS: Granting access to ${featureName} for user ${userId}`);

      // Add timeout to prevent hanging database queries
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Database query timeout')), 1500)
      );

      // Still check if feature exists for proper error handling
      const flagPromise = supabase
        .from('feature_flags')
        .select('*')
        .eq('feature_key', featureName)
        .single();

      const { data: featureFlag, error: flagError } = await Promise.race([flagPromise, timeoutPromise]) as any;

      // Create a mock feature flag if not found
      const mockFeatureFlag: FeatureFlag = {
        id: 'temp-' + featureName,
        feature_key: featureName,
        feature_name: featureName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        is_enabled: true,
        requires_subscription: false, // Temporarily make all features free
        description: `Temporary access to ${featureName}`,
        category: 'premium',
        is_beta: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        usage_count: 0,
        active_users: 0
      };

      const finalFeatureFlag = featureFlag || mockFeatureFlag;

      // Get or create a mock subscription
      let subscription: UserSubscription | null = null;
      try {
        const subscriptionTimeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Subscription query timeout')), 1000)
        );

        const subscriptionPromise = supabase
          .from('user_subscriptions')
          .select('*')
          .eq('user_id', userId)
          .single();

        const { data: existingSubscription } = await Promise.race([subscriptionPromise, subscriptionTimeoutPromise]) as any;

        subscription = existingSubscription || {
          id: 'temp-sub-' + userId,
          user_id: userId,
          plan_type: 'premium', // Temporarily grant premium to all
          status: 'active',
          features: ['telegram_integration', 'advanced_analytics', 'family_collaboration', 'voice_transactions', 'ocr_receipts'],
          expires_at: null,
          stripe_subscription_id: null,
          stripe_customer_id: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
      } catch (subError) {
        // Create temporary subscription
        subscription = {
          id: 'temp-sub-' + userId,
          user_id: userId,
          plan_type: 'premium',
          status: 'active',
          features: ['telegram_integration', 'advanced_analytics', 'family_collaboration', 'voice_transactions', 'ocr_receipts'],
          expires_at: null,
          stripe_subscription_id: null,
          stripe_customer_id: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
      }

      // TEMPORARY: Always grant access
      return {
        hasAccess: true,
        reason: 'Temporary access granted for debugging',
        subscription,
        featureFlag: finalFeatureFlag
      };

    } catch (error) {
      console.error('Error checking feature access:', error);

      // Even on error, grant temporary access
      return {
        hasAccess: true,
        reason: 'Temporary access granted due to error (debugging mode)',
        subscription: {
          id: 'temp-error-sub',
          user_id: userId,
          plan_type: 'premium',
          status: 'active',
          features: ['telegram_integration', 'advanced_analytics', 'family_collaboration'],
          expires_at: null,
          stripe_subscription_id: null,
          stripe_customer_id: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Get all feature flags (admin only)
   */
  async getFeatureFlags(): Promise<FeatureFlag[]> {
    try {
      const { data, error } = await supabase
        .from('feature_flags')
        .select('*')
        .order('feature_name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching feature flags:', error);
      return [];
    }
  }

  /**
   * Update feature flag (admin only)
   */
  async updateFeatureFlag(featureName: string, updates: Partial<FeatureFlag>): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('feature_flags')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('feature_key', featureName);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error updating feature flag:', error);
      return false;
    }
  }

  /**
   * Toggle feature on/off (admin only)
   */
  async toggleFeature(featureName: string, isEnabled: boolean): Promise<boolean> {
    return this.updateFeatureFlag(featureName, { is_enabled: isEnabled });
  }

  /**
   * Record feature usage
   */
  async recordFeatureUsage(userId: string, featureName: string): Promise<void> {
    try {
      // Update or insert feature usage
      const { error: usageError } = await supabase
        .from('feature_usage')
        .upsert({
          user_id: userId,
          feature_name: featureName,
          usage_count: 1,
          last_used: new Date().toISOString()
        }, {
          onConflict: 'user_id,feature_name',
          ignoreDuplicates: false
        });

      if (usageError) {
        console.error('Error recording feature usage:', usageError);
      }

      // Increment global usage count (with fallback if function doesn't exist)
      try {
        const { error: flagError } = await supabase.rpc('increment_feature_usage', {
          feature_name: featureName
        });

        if (flagError) {
          console.warn('RPC function increment_feature_usage not found, using fallback:', flagError);
          // Fallback: Update feature_flags table directly
          await supabase
            .from('feature_flags')
            .update({
              usage_count: supabase.raw('usage_count + 1'),
              updated_at: new Date().toISOString()
            })
            .eq('feature_name', featureName);
        }
      } catch (rpcError) {
        console.warn('RPC call failed, using direct update fallback:', rpcError);
        // Fallback: Update feature_flags table directly
        await supabase
          .from('feature_flags')
          .update({
            usage_count: supabase.raw('usage_count + 1'),
            updated_at: new Date().toISOString()
          })
          .eq('feature_name', featureName);
      }
    } catch (error) {
      console.error('Error recording feature usage:', error);
    }
  }

  /**
   * Get feature usage statistics (admin only)
   */
  async getFeatureUsageStats(featureName: string): Promise<{
    totalUsage: number;
    activeUsers: number;
    usageByDay: Array<{ date: string; count: number }>;
    topUsers: Array<{ user_id: string; usage_count: number; last_used: string }>;
  }> {
    try {
      // Get total usage and active users
      const { data: stats, error: statsError } = await supabase
        .from('feature_flags')
        .select('usage_count, active_users')
        .eq('feature_key', featureName)
        .single();

      if (statsError) throw statsError;

      // Get usage by day (last 30 days)
      const { data: dailyUsage, error: dailyError } = await supabase
        .from('feature_usage')
        .select('last_used, usage_count')
        .eq('feature_name', featureName)
        .gte('last_used', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());

      if (dailyError) throw dailyError;

      // Get top users
      const { data: topUsers, error: topUsersError } = await supabase
        .from('feature_usage')
        .select('user_id, usage_count, last_used')
        .eq('feature_name', featureName)
        .order('usage_count', { ascending: false })
        .limit(10);

      if (topUsersError) throw topUsersError;

      // Process daily usage
      const usageByDay = this.processDailyUsage(dailyUsage || []);

      return {
        totalUsage: stats?.usage_count || 0,
        activeUsers: stats?.active_users || 0,
        usageByDay,
        topUsers: topUsers || []
      };
    } catch (error) {
      console.error('Error fetching feature usage stats:', error);
      return {
        totalUsage: 0,
        activeUsers: 0,
        usageByDay: [],
        topUsers: []
      };
    }
  }

  /**
   * Get user subscription details
   */
  async getUserSubscription(userId: string): Promise<UserSubscription | null> {
    try {
      const { data, error } = await supabase
        .from('user_subscriptions')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'active')
        .single();

      if (error) return null;
      return data;
    } catch (error) {
      console.error('Error fetching user subscription:', error);
      return null;
    }
  }

  /**
   * Create or update user subscription
   */
  async updateUserSubscription(userId: string, subscription: Partial<UserSubscription>): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_subscriptions')
        .upsert({
          user_id: userId,
          ...subscription,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        });

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error updating user subscription:', error);
      return false;
    }
  }

  /**
   * Initialize default feature flags
   */
  async initializeFeatureFlags(): Promise<void> {
    const defaultFeatures = [
      {
        feature_name: 'telegram_integration',
        is_enabled: true,
        is_premium: true,
        description: 'AI-powered Telegram bot integration for voice, text, and photo transactions',
        usage_count: 0,
        active_users: 0
      },
      {
        feature_name: 'advanced_analytics',
        is_enabled: true,
        is_premium: true,
        description: 'Advanced financial analytics and insights',
        usage_count: 0,
        active_users: 0
      },
      {
        feature_name: 'family_collaboration',
        is_enabled: true,
        is_premium: true,
        description: 'Family group financial management and approval workflows',
        usage_count: 0,
        active_users: 0
      },
      {
        feature_name: 'voice_transactions',
        is_enabled: true,
        is_premium: false,
        description: 'Voice message transaction processing',
        usage_count: 0,
        active_users: 0
      },
      {
        feature_name: 'ocr_receipts',
        is_enabled: true,
        is_premium: false,
        description: 'Receipt photo scanning and processing',
        usage_count: 0,
        active_users: 0
      }
    ];

    try {
      for (const feature of defaultFeatures) {
        await supabase
          .from('feature_flags')
          .upsert(feature, { onConflict: 'feature_name' });
      }
    } catch (error) {
      console.error('Error initializing feature flags:', error);
    }
  }

  /**
   * Helper method to process daily usage data
   */
  private processDailyUsage(usageData: any[]): Array<{ date: string; count: number }> {
    const dailyMap = new Map<string, number>();
    
    usageData.forEach(usage => {
      const date = new Date(usage.last_used).toISOString().split('T')[0];
      dailyMap.set(date, (dailyMap.get(date) || 0) + usage.usage_count);
    });

    return Array.from(dailyMap.entries())
      .map(([date, count]) => ({ date, count }))
      .sort((a, b) => a.date.localeCompare(b.date));
  }
}

export const premiumFeatureService = new PremiumFeatureService();
export default premiumFeatureService;
