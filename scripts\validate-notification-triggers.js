#!/usr/bin/env node

/**
 * Notification Triggers Validation Script
 * Validates that all notification trigger functions used in components are properly defined
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 Validating Notification Trigger Functions...\n');

// Get the hook file content
const hookPath = path.join(__dirname, '../src/hooks/useNotificationTriggers.ts');
const hookContent = fs.readFileSync(hookPath, 'utf8');

// Extract all functions returned by the hook
const returnMatch = hookContent.match(/return\s*\{([^}]+)\}/s);
if (!returnMatch) {
  console.error('❌ Could not find return statement in useNotificationTriggers hook');
  process.exit(1);
}

const returnedFunctions = returnMatch[1]
  .split(',')
  .map(line => line.trim())
  .filter(line => line && !line.startsWith('//'))
  .map(line => line.replace(/,$/, ''));

console.log('✅ Functions available in useNotificationTriggers hook:');
returnedFunctions.forEach(func => {
  console.log(`   - ${func}`);
});

// Find all files that import useNotificationTriggers
const srcDir = path.join(__dirname, '../src');
const usageFiles = [];

function findUsageFiles(dir) {
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      findUsageFiles(filePath);
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      const content = fs.readFileSync(filePath, 'utf8');
      if (content.includes('useNotificationTriggers')) {
        usageFiles.push(filePath);
      }
    }
  }
}

findUsageFiles(srcDir);

console.log(`\n🔍 Found ${usageFiles.length} files using useNotificationTriggers:`);

const allUsedFunctions = new Set();
const missingFunctions = [];
const fileIssues = [];

usageFiles.forEach(filePath => {
  const relativePath = path.relative(path.join(__dirname, '..'), filePath);
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Extract destructured imports from useNotificationTriggers
  const destructureMatches = content.match(/const\s*\{\s*([^}]+)\s*\}\s*=\s*useNotificationTriggers\(\)/g);
  
  if (destructureMatches) {
    destructureMatches.forEach(match => {
      const functionsMatch = match.match(/\{\s*([^}]+)\s*\}/);
      if (functionsMatch) {
        const functions = functionsMatch[1]
          .split(',')
          .map(f => f.trim())
          .filter(f => f && !f.startsWith('//'));
        
        functions.forEach(func => {
          allUsedFunctions.add(func);
          
          if (!returnedFunctions.includes(func)) {
            missingFunctions.push(func);
            fileIssues.push({
              file: relativePath,
              function: func,
              issue: 'Function not defined in hook'
            });
          }
        });
      }
    });
  }
  
  console.log(`   - ${relativePath}`);
});

console.log(`\n📊 Analysis Results:`);
console.log(`   Total functions used: ${allUsedFunctions.size}`);
console.log(`   Functions available: ${returnedFunctions.length}`);
console.log(`   Missing functions: ${missingFunctions.length}`);

if (missingFunctions.length > 0) {
  console.log(`\n❌ Missing Functions Found:`);
  [...new Set(missingFunctions)].forEach(func => {
    console.log(`   - ${func}`);
  });
  
  console.log(`\n📁 Files with Issues:`);
  fileIssues.forEach(issue => {
    console.log(`   ❌ ${issue.file}: ${issue.function} - ${issue.issue}`);
  });
  
  console.log(`\n💡 Suggested Fix:`);
  console.log(`   Add the missing functions to src/hooks/useNotificationTriggers.ts`);
  
  process.exit(1);
} else {
  console.log(`\n✅ All notification trigger functions are properly defined!`);
}

// Check for unused functions
const unusedFunctions = returnedFunctions.filter(func => !allUsedFunctions.has(func));
if (unusedFunctions.length > 0) {
  console.log(`\n⚠️  Unused Functions (can be removed if not needed):`);
  unusedFunctions.forEach(func => {
    console.log(`   - ${func}`);
  });
}

console.log(`\n🎯 Validation Summary:`);
console.log(`   ✅ Hook exports: ${returnedFunctions.length} functions`);
console.log(`   ✅ Components use: ${allUsedFunctions.size} functions`);
console.log(`   ✅ Missing functions: ${missingFunctions.length}`);
console.log(`   ✅ Files checked: ${usageFiles.length}`);

if (missingFunctions.length === 0) {
  console.log(`\n🎉 All notification trigger functions are working correctly!`);
} else {
  console.log(`\n⚠️  Please fix the missing functions before proceeding.`);
}

// Generate a summary report
const reportPath = path.join(__dirname, '../NOTIFICATION_TRIGGERS_VALIDATION.md');
const reportContent = `# Notification Triggers Validation Report

Generated: ${new Date().toISOString()}

## Summary
- **Hook exports:** ${returnedFunctions.length} functions
- **Components use:** ${allUsedFunctions.size} functions  
- **Missing functions:** ${missingFunctions.length}
- **Files checked:** ${usageFiles.length}

## Available Functions
${returnedFunctions.map(func => `- \`${func}\``).join('\n')}

## Used Functions
${[...allUsedFunctions].map(func => `- \`${func}\``).join('\n')}

${missingFunctions.length > 0 ? `## Missing Functions
${[...new Set(missingFunctions)].map(func => `- \`${func}\``).join('\n')}

## Files with Issues
${fileIssues.map(issue => `- **${issue.file}**: \`${issue.function}\` - ${issue.issue}`).join('\n')}
` : '## ✅ All Functions Available'}

${unusedFunctions.length > 0 ? `## Unused Functions
${unusedFunctions.map(func => `- \`${func}\``).join('\n')}
` : ''}

## Status
${missingFunctions.length === 0 ? '✅ **PASSED** - All notification trigger functions are properly defined' : '❌ **FAILED** - Missing functions need to be implemented'}
`;

fs.writeFileSync(reportPath, reportContent);
console.log(`\n📄 Detailed report saved to: NOTIFICATION_TRIGGERS_VALIDATION.md`);
