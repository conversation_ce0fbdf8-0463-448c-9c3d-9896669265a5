-- Fix Database Schema Issues
-- Run this in your Supabase SQL Editor to fix missing functions and tables

-- 1. Create or replace the increment_feature_usage function
CREATE OR REPLACE FUNCTION increment_feature_usage(feature_name TEXT)
RETURNS void AS $$
BEGIN
    -- Update feature flags usage count
    UPDATE feature_flags 
    SET usage_count = usage_count + 1,
        updated_at = NOW()
    WHERE feature_flags.feature_name = increment_feature_usage.feature_name;
    
    -- If no rows were updated, the feature doesn't exist, so insert it
    IF NOT FOUND THEN
        INSERT INTO feature_flags (feature_name, usage_count, is_enabled, is_premium)
        VALUES (increment_feature_usage.feature_name, 1, true, false)
        ON CONFLICT (feature_name) DO UPDATE SET
            usage_count = feature_flags.usage_count + 1,
            updated_at = NOW();
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Ensure telegram_users table exists with correct schema
CREATE TABLE IF NOT EXISTS telegram_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    telegram_id BIGINT UNIQUE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    username TEXT,
    first_name TEXT,
    last_name TEXT,
    language_code TEXT DEFAULT 'en',
    linked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create indexes for telegram_users if they don't exist
CREATE INDEX IF NOT EXISTS idx_telegram_users_telegram_id ON telegram_users(telegram_id);
CREATE INDEX IF NOT EXISTS idx_telegram_users_user_id ON telegram_users(user_id);
CREATE INDEX IF NOT EXISTS idx_telegram_users_active ON telegram_users(is_active) WHERE is_active = true;

-- 4. Enable RLS on telegram_users
ALTER TABLE telegram_users ENABLE ROW LEVEL SECURITY;

-- 5. Create RLS policies for telegram_users
DROP POLICY IF EXISTS "Users can view own telegram account" ON telegram_users;
CREATE POLICY "Users can view own telegram account" ON telegram_users
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update own telegram account" ON telegram_users;
CREATE POLICY "Users can update own telegram account" ON telegram_users
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert own telegram account" ON telegram_users;
CREATE POLICY "Users can insert own telegram account" ON telegram_users
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 6. Ensure feature_flags table exists
CREATE TABLE IF NOT EXISTS feature_flags (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    feature_name TEXT UNIQUE NOT NULL,
    is_enabled BOOLEAN DEFAULT true,
    is_premium BOOLEAN DEFAULT false,
    description TEXT,
    usage_count INTEGER DEFAULT 0,
    active_users INTEGER DEFAULT 0,
    category TEXT DEFAULT 'general',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Insert default feature flags if they don't exist
INSERT INTO feature_flags (feature_name, is_enabled, is_premium, description, category) VALUES
('telegram_integration', true, true, 'Telegram bot integration for transaction management', 'integration'),
('voice_transactions', true, true, 'Voice-to-text transaction entry', 'input'),
('ocr_receipts', true, true, 'OCR receipt scanning and processing', 'input'),
('advanced_analytics', true, true, 'Advanced financial analytics and insights', 'analytics'),
('export_data', true, false, 'Export financial data to various formats', 'data'),
('custom_categories', true, false, 'Create custom transaction categories', 'organization'),
('budget_alerts', true, false, 'Automated budget alerts and notifications', 'alerts'),
('investment_tracking', true, true, 'Investment portfolio tracking and analysis', 'investment'),
('bill_reminders', true, false, 'Automated bill payment reminders', 'alerts'),
('multi_currency', true, true, 'Multi-currency support and conversion', 'currency')
ON CONFLICT (feature_name) DO NOTHING;

-- 8. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT, INSERT, UPDATE ON telegram_users TO authenticated;
GRANT SELECT ON feature_flags TO authenticated;
GRANT EXECUTE ON FUNCTION increment_feature_usage(TEXT) TO authenticated;

-- 9. Create function to get unread notification count (if missing)
CREATE OR REPLACE FUNCTION get_unread_notification_count(user_id UUID DEFAULT auth.uid())
RETURNS INTEGER AS $$
BEGIN
    RETURN (
        SELECT COUNT(*)::INTEGER
        FROM notifications 
        WHERE notifications.user_id = get_unread_notification_count.user_id
        AND read_at IS NULL
        AND (expires_at IS NULL OR expires_at > NOW())
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

GRANT EXECUTE ON FUNCTION get_unread_notification_count(UUID) TO authenticated;

-- 10. Add helpful comments
COMMENT ON FUNCTION increment_feature_usage(TEXT) IS 'Increments usage count for a feature flag';
COMMENT ON FUNCTION get_unread_notification_count(UUID) IS 'Returns count of unread notifications for a user';
COMMENT ON TABLE telegram_users IS 'Links Telegram accounts to FiNManageR users';
COMMENT ON TABLE feature_flags IS 'Feature flags for premium and standard features';
